-- 彩票店管理系统客户端

-- 调试打印函数
local function DebugPrint(message, level)
    -- 只有在调试模式开启时才打印
    if Config.Debug == true then
        level = level or "^7" -- 默认白色
        print(level .. "[彩票系统] " .. message)
    end
end

-- 全局变量
local adminSystemOpen = false
local adminData = nil
local currentTab = 'sales'
local adminNPC = nil
local adminBlip = nil

-- 初始化管理系统
Citizen.CreateThread(function()
    -- 等待资源完全启动
    Citizen.Wait(1000)
    
    -- 检查是否启用职业系统
    if Config.LotteryJob.enabled ~= true then
        DebugPrint("^3[彩票系统] ^7职业系统未启用，不创建管理系统交互")
        return
    end
    
    -- 创建管理员NPC
    CreateAdminNPC()
    
    -- 注册交互区域
    RegisterAdminZone()
end)

-- 创建管理员NPC
function CreateAdminNPC()
    -- 获取NPC配置
    local npcConfig = Config.AdminNPC or {
        model = "a_f_y_business_02",
        coords = vector4(372.875, 328.896, 103.566, 255.118), -- 默认位置在彩票店附近
        scenario = "WORLD_HUMAN_CLIPBOARD"
    }
    
    -- 检查是否启用NPC创建
    if npcConfig.createPed ~= false then
        -- 请求模型
        local modelHash = GetHashKey(npcConfig.model)
        RequestModel(modelHash)
        while not HasModelLoaded(modelHash) do
            Citizen.Wait(10)
        end
        
        -- 创建NPC
        adminNPC = CreatePed(4, modelHash, npcConfig.coords.x, npcConfig.coords.y, npcConfig.coords.z - 1.0, npcConfig.coords.w, false, true)
        SetEntityHeading(adminNPC, npcConfig.coords.w)
        FreezeEntityPosition(adminNPC, true)
        -- 设置NPC为完全无敌
        SetEntityCanBeDamaged(adminNPC, false)
        SetEntityInvincible(adminNPC, true)
        SetPedCanRagdollFromPlayerImpact(adminNPC, false)
        SetBlockingOfNonTemporaryEvents(adminNPC, true)
        SetPedFleeAttributes(adminNPC, 0, 0)
        SetPedCombatAttributes(adminNPC, 17, 1)
        SetPedCanBeTargetted(adminNPC, false)
        SetPedCanBeKnockedOffVehicle(adminNPC, 1)
        SetPedCanBeDraggedOut(adminNPC, false)
        SetEntityProofs(adminNPC, true, true, true, true, true, true, true, true)
        
        -- 设置NPC动作
        if npcConfig.scenario then
            TaskStartScenarioInPlace(adminNPC, npcConfig.scenario, 0, true)
        end
        
        -- 释放模型
        SetModelAsNoLongerNeeded(modelHash)
        
        -- 为NPC添加交互选项
        if GetResourceState('ox_target') == 'started' then
            -- 为NPC添加交互选项
            if adminNPC then
                exports.ox_target:addLocalEntity(adminNPC, {
                    {
                        name = 'lottery_admin_system',
                        icon = 'fas fa-cogs',
                        label = '访问彩票店管理系统',
                        canInteract = function()
                            -- 这里可以添加额外的交互条件检查
                            return true
                        end,
                        onSelect = function()
                            TriggerServerEvent('lottery:getAdminData')
                            TriggerEvent('lottery:notification', '彩票店管理系统', '正在检查您的职业权限...', 'info')
                        end
                    }
                })
                
                DebugPrint("^2[彩票系统] ^7已为管理员NPC添加ox_target交互")
            end
        end
        
        DebugPrint("^2[彩票系统] ^7管理员NPC创建成功")
    else
        DebugPrint("^3[彩票系统] ^7管理员NPC创建已禁用，创建交互点")
        -- 如果不创建NPC，则创建一个交互点
        CreateAdminInteractionPoint(npcConfig)
    end
    
    -- 创建地图标记，独立于NPC创建
    if npcConfig.createBlip == true and npcConfig.blip then
        adminBlip = AddBlipForCoord(npcConfig.coords.x, npcConfig.coords.y, npcConfig.coords.z)
        SetBlipSprite(adminBlip, npcConfig.blip.sprite or 500)
        SetBlipDisplay(adminBlip, 4)
        SetBlipScale(adminBlip, npcConfig.blip.scale or 0.8)
        SetBlipColour(adminBlip, npcConfig.blip.color or 46)
        SetBlipAsShortRange(adminBlip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(npcConfig.blip.name or "彩票店管理员")
        EndTextCommandSetBlipName(adminBlip)
        
        DebugPrint("^2[彩票系统] ^7管理员BLIP创建成功")
    else
        DebugPrint("^3[彩票系统] ^7管理员BLIP创建已禁用")
    end
end

-- 创建管理员交互点
function CreateAdminInteractionPoint(npcConfig)
    local coords = vector3(npcConfig.coords.x, npcConfig.coords.y, npcConfig.coords.z)
    
    -- 使用ox_target创建交互区域
    if GetResourceState('ox_target') == 'started' then
        exports.ox_target:addSphereZone({
            coords = coords,
            radius = 1.5,
            options = {
                {
                    name = 'lottery_admin_system_zone',
                    icon = 'fas fa-cogs',
                    label = '访问彩票店管理系统',
                    onSelect = function()
                        TriggerServerEvent('lottery:getAdminData')
                        TriggerEvent('lottery:notification', '彩票店管理系统', '正在检查您的职业权限...', 'info')
                    end
                }
            }
        })
        DebugPrint("^2[彩票系统] ^7管理员交互点(ox_target)创建成功")
    elseif GetResourceState('qb-target') == 'started' then
        -- 使用qb-target创建交互区域
        exports['qb-target']:AddCircleZone('lottery_admin_system_zone', coords, 1.5, {
            name = 'lottery_admin_system_zone',
            debugPoly = false,
        }, {
            options = {
                {
                    type = "client",
                    event = "lottery:accessAdminSystem",
                    icon = "fas fa-cogs",
                    label = "访问彩票店管理系统"
                },
            },
            distance = 2.0
        })
        DebugPrint("^2[彩票系统] ^7管理员交互点(qb-target)创建成功")
    else
        -- 如果没有target系统，使用传统的交互检测循环
        Citizen.CreateThread(function()
            while true do
                Citizen.Wait(0)
                
                -- 获取玩家位置
                local playerPed = PlayerPedId()
                local playerCoords = GetEntityCoords(playerPed)
                local distance = #(playerCoords - coords)
                
                -- 在交互范围内
                if distance < 2.0 then
                    -- 显示提示
                    DrawText3D(coords.x, coords.y, coords.z, "按 ~y~E~w~ 访问~b~彩票店管理系统~w~（仅限彩票店职业）")
                    
                    -- 检测按键
                    if IsControlJustReleased(0, 38) then -- E键
                        TriggerServerEvent('lottery:getAdminData')
                        TriggerEvent('lottery:notification', '彩票店管理系统', '正在检查您的职业权限...', 'info')
                    end
                end
            end
        end)
        DebugPrint("^2[彩票系统] ^7管理员交互点(3D文本)创建成功")
    end
end

-- 注册交互区域
function RegisterAdminZone()
    -- 如果已经创建了NPC，则不需要再创建交互区域
    if adminNPC then
        return
    end
    
    -- 获取NPC配置
    local npcConfig = Config.AdminNPC or {
        coords = vector4(372.875, 328.896, 103.566, 255.118)
    }
    
    -- 如果没有ox_target，回退到传统的交互方式
    if GetResourceState('ox_target') ~= 'started' and GetResourceState('qb-target') ~= 'started' then
        DebugPrint("^3[彩票系统] ^7未检测到target资源，使用传统交互方式")
        
        -- 交互检测循环已在CreateAdminInteractionPoint中创建，此处不需要重复
    end
end

-- 注册访问管理系统事件（供qb-target使用）
RegisterNetEvent('lottery:accessAdminSystem')
AddEventHandler('lottery:accessAdminSystem', function()
    TriggerServerEvent('lottery:getAdminData')
    TriggerEvent('lottery:notification', '彩票店管理系统', '正在检查您的职业权限...', 'info')
end)

-- 3D文本绘制函数
function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())
    
    SetTextScale(0.35, 0.35)
    SetTextFont(0)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x, _y)
    local factor = (string.len(text)) / 370
    DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 41, 11, 41, 68)
end

-- 注册NUI回调
RegisterNUICallback('openAdminSystem', function(data, cb)
    TriggerServerEvent('lottery:getAdminData')
    cb({})
end)

-- 关闭管理系统
RegisterNUICallback('closeAdminSystem', function(data, cb)
    SetNuiFocus(false, false)
    adminSystemOpen = false
    cb({})
end)

-- 获取管理系统数据
RegisterNUICallback('getAdminData', function(data, cb)
    TriggerServerEvent('lottery:getAdminData')
    cb({})
end)

-- 获取账户数据
RegisterNUICallback('getAccountData', function(data, cb)
    DebugPrint("^2[彩票系统] ^7请求刷新账户数据")
    -- 向服务器请求最新账户数据
    TriggerServerEvent('lottery:getShopAccount')
    -- 返回成功响应
    cb({success = true})
end)

-- 切换标签页
RegisterNUICallback('switchAdminTab', function(data, cb)
    currentTab = data.tab
    cb({})
end)

-- 存取款操作
RegisterNUICallback('manageAccount', function(data, cb)
    TriggerServerEvent('lottery:manageAccount', data.action, data.amount)
    cb({})
end)

-- 接收管理系统数据
RegisterNetEvent('lottery:receiveAdminData')
AddEventHandler('lottery:receiveAdminData', function(data)
    if data then
        DebugPrint("^2[彩票系统] ^7接收到管理系统数据")
        
        -- 检查销售数据
        if data.sales and data.sales.total then
            DebugPrint("^2[彩票系统] ^7销售数据: 总销量=" .. (data.sales.total.total_sold or 0) ..
                ", 总收入=" .. (data.sales.total.total_revenue or 0) ..
                ", 刮刮乐销量=" .. (data.sales.total.total_scratch_sold or 0) ..
                ", 彩票销量=" .. (data.sales.total.total_lottery_sold or 0))
        else
            DebugPrint("^1[彩票系统] ^7警告: 销售数据为空")
        end
    else
        DebugPrint("^1[彩票系统] ^7错误: 接收到的管理系统数据为空")
    end
    
    adminData = data
    OpenAdminSystem()
    
    -- 转发数据到NUI
    SendNUIMessage({
        action = 'receiveAdminData',
        data = data
    })
end)

-- 更新账户余额
RegisterNetEvent('lottery:updateAccountBalance')
AddEventHandler('lottery:updateAccountBalance', function(amount, isDeposit, accountData, success, playerCash, transactions)
    -- 如果服务器发送了完整的账户数据，则直接使用
    if accountData then
        -- 更新本地缓存的账户数据
        if adminData and adminData.account then
            adminData.account = accountData
            
            -- 如果服务器发送了玩家现金数据，也更新它
            if playerCash ~= nil then
                if not adminData.playerData then
                    adminData.playerData = {}
                end
                adminData.playerData.money = playerCash
                DebugPrint("^2[彩票系统] ^7更新玩家现金数据: " .. playerCash)
            end
            
            -- 如果服务器发送了交易明细数据，也更新它
            if transactions then
                adminData.transactions = transactions
                DebugPrint("^2[彩票系统] ^7更新交易明细数据: " .. #transactions .. "条记录")
            end
        end
        
        -- 立即发送更新信息到前端，传递操作成功状态、玩家现金和交易明细
        SendNUIMessage({
            action = 'updateAccountBalance',
            account = accountData,
            success = success ~= false, -- 如果success未指定，默认为true
            playerCash = playerCash,
            transactions = transactions
        })
        
        -- 打印调试信息
        DebugPrint("^2[彩票系统] ^7账户余额已更新:", 
              "操作类型=" .. (isDeposit and "存款" or "取款"), 
              "金额=" .. (amount or 0), 
              "当前余额=" .. accountData.balance,
              "操作成功=" .. (success ~= false and "是" or "否"),
              "玩家现金=" .. (playerCash or "未知"))
        
        -- 只有在操作成功时才显示成功提示
        if amount and amount > 0 and success ~= false then
            local actionText = isDeposit and '存入' or '取出'
            TriggerEvent('lottery:notification', '操作成功', '成功' .. actionText .. ' ¥' .. amount .. (isDeposit and ' 到' or ' 从') .. '彩票店账户', 'success')
        end
    else
        -- 兼容旧版本，如果没有接收到完整账户数据，则使用旧的更新方式
        if adminData and adminData.account then
            -- 只有在操作成功时才更新本地数据
            if success ~= false then
                if isDeposit then
                    adminData.account.balance = adminData.account.balance + amount
                    adminData.account.total_income = adminData.account.total_income + amount
                else
                    adminData.account.balance = adminData.account.balance - amount
                    adminData.account.total_payout = adminData.account.total_payout + amount
                end
                
                -- 如果服务器发送了玩家现金数据，也更新它
                if playerCash ~= nil then
                    if not adminData.playerData then
                        adminData.playerData = {}
                    end
                    adminData.playerData.money = playerCash
                    DebugPrint("^2[彩票系统] ^7更新玩家现金数据: " .. playerCash)
                end
                
                -- 如果服务器发送了交易明细数据，也更新它
                if transactions then
                    adminData.transactions = transactions
                    DebugPrint("^2[彩票系统] ^7更新交易明细数据: " .. #transactions .. "条记录")
                end
            end
            
            -- 更新最后更新时间，使用当前时间戳代替os.date
            local now = GetGameTimer()
            
            -- 立即发送更新信息到前端
            SendNUIMessage({
                action = 'updateAccountBalance',
                account = adminData.account,
                success = success ~= false, -- 如果success未指定，默认为true
                playerCash = playerCash,
                transactions = transactions
            })
            
            -- 只有在操作成功时才显示成功提示
            if success ~= false then
                local actionText = isDeposit and '存入' or '取出'
                TriggerEvent('lottery:notification', '操作成功', '成功' .. actionText .. ' ¥' .. amount .. (isDeposit and ' 到' or ' 从') .. '彩票店账户', 'success')
            end
            
            -- 打印调试信息
            DebugPrint("^2[彩票系统] ^7账户余额已更新:", 
                  "操作类型=" .. (isDeposit and "存款" or "取款"), 
                  "金额=" .. amount, 
                  "当前余额=" .. adminData.account.balance,
                  "操作成功=" .. (success ~= false and "是" or "否"),
                  "玩家现金=" .. (playerCash or "未知"))
        else
            DebugPrint("^1[彩票系统] ^7错误: 无法更新账户余额，账户数据不存在")
            
            -- 请求最新数据
            TriggerServerEvent('lottery:getAdminData')
        end
    end
end)

-- 打开管理系统
function OpenAdminSystem()
    if adminSystemOpen then return end
    
    adminSystemOpen = true
    SetNuiFocus(true, true)
    
    -- 使用单独的管理系统HTML页面
    SendNUIMessage({
        action = 'openAdminSystem',
        data = adminData,
        currentTab = currentTab
    })
    
    -- 打开管理系统页面
    SetNuiFocusKeepInput(false)
    TriggerEvent('lottery:openAdminUI')
end

-- 注册打开管理系统UI事件
RegisterNetEvent('lottery:openAdminUI')
AddEventHandler('lottery:openAdminUI', function()
    -- 显示管理系统页面
    SetNuiFocus(true, true)
end)

-- 处理前端发来的通知请求
RegisterNUICallback('showNotification', function(data, cb)
    -- 显示游戏内通知
    TriggerEvent('lottery:notification', data.title, data.message, data.type)
    cb({success = true})
end)

-- 员工管理相关事件
RegisterNUICallback('hireEmployee', function(data, cb)
    TriggerServerEvent('lottery:hireEmployee', data.employeeId, data.employeeName, data.level, data.salary)
    cb({})
end)

RegisterNUICallback('fireEmployee', function(data, cb)
    TriggerServerEvent('lottery:fireEmployee', data.employeeId, data.reason)
    cb({})
end)

RegisterNUICallback('updateEmployeeLevel', function(data, cb)
    TriggerServerEvent('lottery:updateEmployeeLevel', data.employeeId, data.newLevel)
    cb({})
end)

RegisterNUICallback('updateEmployeeSalary', function(data, cb)
    TriggerServerEvent('lottery:updateEmployeeSalary', data.employeeId, data.newSalary)
    cb({})
end)

RegisterNUICallback('payEmployeeSalary', function(data, cb)
    TriggerServerEvent('lottery:payEmployeeSalary')
    cb({})
end)

RegisterNUICallback('getEmployeeLogs', function(data, cb)
    TriggerServerEvent('lottery:getEmployeeLogs', data.employeeId)
    cb({})
end)

-- 获取员工列表
RegisterNUICallback('getEmployees', function(data, cb)
    DebugPrint("^2[彩票系统] ^7前端请求员工列表")
    TriggerServerEvent('lottery:getEmployees')
    cb({success = true})
end)

-- 接收员工列表更新
RegisterNetEvent('lottery:updateEmployeeList')
AddEventHandler('lottery:updateEmployeeList', function(employees)
    SendNUIMessage({
        action = 'updateEmployeeList',
        employees = employees
    })
end)

-- 接收员工日志
RegisterNetEvent('lottery:receiveEmployeeLogs')
AddEventHandler('lottery:receiveEmployeeLogs', function(employeeId, logs)
    SendNUIMessage({
        action = 'receiveEmployeeLogs',
        employeeId = employeeId,
        logs = logs
    })
end)

-- 接收职业等级列表
RegisterNetEvent('lottery:receiveJobGrades')
AddEventHandler('lottery:receiveJobGrades', function(jobGrades)
    SendNUIMessage({
        action = 'receiveJobGrades',
        jobGrades = jobGrades
    })
end)

-- 接收员工等级更新事件
RegisterNetEvent('lottery:refreshEmployeeLevel')
AddEventHandler('lottery:refreshEmployeeLevel', function(data)
    DebugPrint("^2[彩票系统] ^7接收到员工等级更新: 员工ID=" .. (data.employeeId or "未知") .. ", 新等级=" .. (data.newLevel or "未知"))
    
    -- 转发数据到NUI界面
    SendNUIMessage({
        action = 'refreshEmployeeLevel',
        employeeId = data.employeeId,
        newLevel = data.newLevel,
        employeeName = data.employeeName
    })
end)

-- 接收员工薪资更新事件
RegisterNetEvent('lottery:refreshEmployeeSalary')
AddEventHandler('lottery:refreshEmployeeSalary', function(data)
    DebugPrint("^2[彩票系统] ^7接收到员工薪资更新: 员工ID=" .. (data.employeeId or "未知") .. ", 新薪资=" .. (data.newSalary or "未知"))
    
    -- 转发数据到NUI界面
    SendNUIMessage({
        action = 'refreshEmployeeSalary',
        employeeId = data.employeeId,
        newSalary = data.newSalary,
        employeeName = data.employeeName
    })
end)

-- 接收刷新员工界面事件
RegisterNetEvent('lottery:refreshEmployeeUI')
AddEventHandler('lottery:refreshEmployeeUI', function()
    DebugPrint("^2[彩票系统] ^7接收到刷新员工界面事件")
    
    -- 发送事件到NUI界面，强制刷新整个员工界面
    SendNUIMessage({
        action = 'refreshEmployeeUI'
    })
    
    -- 延迟一点时间后，重新请求最新的员工列表数据
    Citizen.SetTimeout(300, function()
        TriggerServerEvent('lottery:getEmployees')
    end)
end)

-- 请求职业等级列表
RegisterNUICallback('getJobGrades', function(data, cb)
    DebugPrint("^2[彩票系统] ^7前端请求职业等级列表")
    TriggerServerEvent('lottery:getJobGrades')
    cb({success = true})
end)

-- 接收直接员工更新事件
RegisterNetEvent('lottery:directEmployeeUpdate')
AddEventHandler('lottery:directEmployeeUpdate', function(data)
    -- 添加数据有效性检查
    if not data or not data.type then
        DebugPrint("^3[彩票系统] ^7接收到无效的员工更新数据")
        return
    end
    
    local employeeId = "未知"
    if data.employee and data.employee.employee_id then
        employeeId = data.employee.employee_id
    end
    
    DebugPrint("^2[彩票系统] ^7接收到直接员工更新: 类型=" .. data.type .. ", 员工ID=" .. employeeId)
    
    -- 确保数据有效后再转发到NUI界面
    if data.employee then
        SendNUIMessage({
            action = 'directEmployeeUpdate',
            updateType = data.type,
            employee = data.employee
        })
    end
end)

-- 接收完整员工数据更新事件
RegisterNetEvent('lottery:completeEmployeeData')
AddEventHandler('lottery:completeEmployeeData', function(employees)
    DebugPrint("^2[彩票系统] ^7接收到完整员工数据更新，共" .. #employees .. "条记录")
    
    -- 更新全局数据
    if adminData then
        adminData.employees = employees
    end
    
    -- 转发完整数据到NUI界面
    SendNUIMessage({
        action = 'updateCompleteEmployeeData',
        employees = employees
    })
end)

-- 获取附近玩家
RegisterNUICallback('getNearbyPlayers', function(data, cb)
    DebugPrint("^2[彩票系统] ^7接收到获取附近玩家请求")
    
    -- 获取玩家位置
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local nearbyPlayers = {}
    
    -- 获取所有玩家
    local players = GetActivePlayers()
    for _, playerId in ipairs(players) do
        -- 排除自己
        if playerId ~= PlayerId() then
            local targetPed = GetPlayerPed(playerId)
            local targetCoords = GetEntityCoords(targetPed)
            local distance = #(playerCoords - targetCoords)
            
            -- 只获取10米范围内的玩家
            if distance <= 10.0 then
                local serverId = GetPlayerServerId(playerId)
                local playerName = GetPlayerName(playerId)
                
                table.insert(nearbyPlayers, {
                    id = serverId,
                    name = playerName,
                    distance = math.floor(distance * 10) / 10 -- 保留一位小数
                })
            end
        end
    end
    
    -- 按距离排序
    table.sort(nearbyPlayers, function(a, b)
        return a.distance < b.distance
    end)
    
    DebugPrint("^2[彩票系统] ^7找到附近玩家: " .. #nearbyPlayers .. "个")
    
    -- 返回附近玩家列表
    cb({
        success = true,
        players = nearbyPlayers
    })
end)

-- 注册手续费分成设置更新事件
RegisterNetEvent('lottery:updateEmployeeCommissionSettings')
AddEventHandler('lottery:updateEmployeeCommissionSettings', function(data)
    -- 发送数据到NUI
    SendNUIMessage({
        action = 'updateEmployeeCommissionSettings',
        settings = data.settings,
        success = data.success
    })
    
    -- 如果成功，显示通知
    if data.success then
        TriggerEvent('lottery:notification', '手续费分成设置', '设置已保存', 'success')
    end
end)

-- 获取员工手续费分成设置 (NUI回调)
RegisterNUICallback('caipiaoc:getEmployeeCommissionSettings', function(data, cb)
    -- 将请求转发到服务器
    TriggerServerEvent('caipiaoc:getEmployeeCommissionSettings', data)
    -- 返回空响应，实际结果将通过事件返回
    cb({})
end)

-- 保存员工手续费分成设置 (NUI回调)
RegisterNUICallback('caipiaoc:saveEmployeeCommissionSettings', function(data, cb)
    -- 将请求转发到服务器
    TriggerServerEvent('caipiaoc:saveEmployeeCommissionSettings', data)
    -- 返回临时成功响应，实际结果将通过事件返回
    cb({success = true})
end)