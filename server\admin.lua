-- 彩票店管理系统服务器端

-- 调试打印函数 - 只在调试模式开启时打印
local function DebugPrint(message, level)
    if Config.Debug and Config.Debug.enabled then
        level = level or "^7" -- 默认白色
        print(level .. "[彩票系统-调试] " .. message)
    end
end

-- 系统消息打印函数 - 总是打印重要的系统消息
local function SystemPrint(message, level)
    level = level or "^7" -- 默认白色
    print(level .. "[彩票系统] " .. message)
end

-- 缓存系统
local CacheSystem = {
    data = {},          -- 缓存的数据
    timestamp = {},     -- 缓存的时间戳
    expiry = {          -- 各类数据的过期时间（秒）
        salesData = 60,        -- 销售数据缓存5分钟
        winningRecords = 5,   -- 中奖记录缓存3分钟
        claimRecords = 5,     -- 兑奖记录缓存3分钟
        unclaimedRecords = 5, -- 未兑奖记录缓存3分钟
        shopAccount = 1,       -- 账户余额缓存1分钟
        transactions = 120,     -- 交易明细缓存2分钟
        employees = 1,        -- 员工列表缓存2分钟
        jobGrades = 3600        -- 职业等级缓存1小时
    }
}

-- 获取缓存数据
function GetCachedData(key, forceRefresh, callback, ...)
    local currentTime = os.time()
    local args = {...}
    
    -- 检查缓存是否存在且未过期
    if not forceRefresh and CacheSystem.data[key] and CacheSystem.timestamp[key] and 
       (currentTime - CacheSystem.timestamp[key] < CacheSystem.expiry[key]) then
        if callback then
            callback(CacheSystem.data[key])
        end
        return true
    end
    
    return false
end

-- 设置缓存数据
function SetCacheData(key, data)
    CacheSystem.data[key] = data
    CacheSystem.timestamp[key] = os.time()
end

-- 清除指定缓存
function ClearCache(key)
    if key then
        CacheSystem.data[key] = nil
        CacheSystem.timestamp[key] = nil
    end
end

-- 清除所有缓存
function ClearAllCache()
    CacheSystem.data = {}
    CacheSystem.timestamp = {}
end

-- 调试打印函数 - 只在调试模式开启时打印
local function DebugPrint(message, level)
    if Config.Debug and Config.Debug.enabled then
        level = level or "^7" -- 默认白色
        print(level .. "[彩票系统-调试] " .. message)
    end
end

-- 系统消息打印函数 - 总是打印重要的系统消息
local function SystemPrint(message, level)
    level = level or "^7" -- 默认白色
    print(level .. "[彩票系统] " .. message)
end

-- 全局变量
local AdminSystem = {}
local Framework = nil

-- 初始化框架
Citizen.CreateThread(function()
    -- 等待main.lua中的框架初始化完成
    while not Config.Framework do
        Wait(100)
    end

    -- 使用Config.Framework获取框架对象
    if Config.Framework == "ESX" then
        Framework = exports["es_extended"]:getSharedObject()
    elseif Config.Framework == "QB" then
        Framework = exports['qb-core']:GetCoreObject()
        -- 确保全局QBCore可用
        if not QBCore then
            QBCore = Framework
        end
    end

    if Framework == nil then
        print("^1[彩票系统] ^7错误: admin.lua无法获取框架对象")
        Wait(500)
        return
    end

    InitializeAdminSystem()
end)

-- 初始化管理系统
function InitializeAdminSystem()
    -- 创建管理系统数据库表
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `lottery_admin_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `admin_id` varchar(50) NOT NULL,
            `admin_name` varchar(100) NOT NULL,
            `action` varchar(100) NOT NULL,
            `details` text NOT NULL,
            `amount` int(11) DEFAULT 0,
            `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `admin_id` (`admin_id`),
            KEY `action` (`action`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]], {})
    
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `lottery_shop_accounts` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `balance` bigint(20) DEFAULT 0,
            `total_income` bigint(20) DEFAULT 0,
            `total_payout` bigint(20) DEFAULT 0,
            `last_updated` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]], {})
    
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `lottery_transactions` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `transaction_type` varchar(50) NOT NULL,
            `amount` bigint(20) NOT NULL,
            `description` text,
            `player_id` varchar(50) DEFAULT NULL,
            `player_name` varchar(100) DEFAULT NULL,
            `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `transaction_type` (`transaction_type`),
            KEY `player_id` (`player_id`),
            KEY `timestamp` (`timestamp`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]], {})
    
    -- 创建员工管理相关的数据库表
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `lottery_employees` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `employee_id` varchar(50) NOT NULL,
            `employee_name` varchar(100) NOT NULL,
            `level` int(11) DEFAULT 1,
            `salary` int(11) DEFAULT 0,
            `bonus` int(11) DEFAULT 0,
            `hire_date` timestamp DEFAULT CURRENT_TIMESTAMP,
            `last_payment` timestamp DEFAULT CURRENT_TIMESTAMP,
            `status` varchar(20) DEFAULT 'active',
            `fee_commission_enabled` TINYINT(1) DEFAULT 0,
            `fee_commission_rate` FLOAT DEFAULT 0.1,
            `fee_commission_min_amount` INT DEFAULT 500,
            PRIMARY KEY (`id`),
            UNIQUE KEY `employee_id` (`employee_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]], {})
    
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `lottery_employee_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `employee_id` varchar(50) NOT NULL,
            `employee_name` varchar(100) NOT NULL,
            `action` varchar(50) NOT NULL,
            `details` text,
            `admin_id` varchar(50) DEFAULT NULL,
            `admin_name` varchar(100) DEFAULT NULL,
            `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `employee_id` (`employee_id`),
            KEY `action` (`action`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]], {})
    
    -- 检查彩票店账户是否存在，如果不存在则创建
    MySQL.Async.fetchScalar('SELECT COUNT(*) FROM lottery_shop_accounts', {}, function(count)
        if count == 0 then
            MySQL.Async.execute('INSERT INTO lottery_shop_accounts (balance, total_income, total_payout) VALUES (0, 0, 0)', {})
            SystemPrint("^2[彩票系统] ^7彩票店账户已创建")
        end
    end)
    
    SystemPrint("^2[彩票系统] ^7管理系统数据库表已初始化")
    
    -- 注册服务器事件
    RegisterServerEvents()
end

-- 注册服务器事件
function RegisterServerEvents()
    -- 获取管理系统数据
    RegisterNetEvent('lottery:getAdminData')
    AddEventHandler('lottery:getAdminData', function()
        local src = source
        
        -- 检查职业系统是否启用
        if Config.LotteryJob.enabled ~= true then
            -- 如果职业系统未启用，检查是否有管理员权限
            local hasPermission = HasAdminPermission(src)
            if not hasPermission then
                -- 如果没有管理员权限，则不允许访问
                TriggerClientEvent('lottery:notification', src, '您没有权限访问管理系统', 'error')
            return
        end
        else
            -- 如果职业系统已启用，检查职业等级是否符合要求
            local hasJobPermission = HasJobPermission(src)
            if not hasJobPermission then
                TriggerClientEvent('lottery:notification', src, '您没有权限访问管理系统', 'error')
            return
        end
        end
        
        -- 获取管理系统数据
        GetAdminSystemData(src)
    end)
    
    -- 获取彩票配置
    RegisterNetEvent('lottery:getLotteryConfig')
    AddEventHandler('lottery:getLotteryConfig', function()
        local src = source

        DebugPrint("收到获取彩票配置请求，来源: " .. src)
        DebugPrint("当前Config状态 - 双色球价格: " .. Config.DoubleBall.price .. " 一等奖: " .. Config.DoubleBall.prizes[1].amount)

        -- 暂时禁用权限检查进行测试
        -- if not IsBossPermission(src) then
        --     -- 获取玩家当前职业等级
        --     local player = GetPlayerFromId(src)
        --     local currentGrade = 0
        --     local requiredGrade = Config.LotteryJob.adminAccess
        --
        --     if Config.Framework == "ESX" then
        --         local job = player.getJob()
        --         currentGrade = job and job.grade or 0
        --     elseif Config.Framework == "QB" then
        --         local job = player.PlayerData.job
        --         currentGrade = job and job.grade.level or 0
        --     end
        --
        --     TriggerClientEvent('lottery:notification', src, '⚠️ 权限不足 ⚠️', '访问彩票配置需要职业等级≥'..requiredGrade..'级，您当前为'..currentGrade..'级', 'error')
        --     return
        -- end
        
        -- 获取配置数据
        local configData = GetLotteryConfigData()

        -- 添加详细调试信息
        DebugPrint("准备发送彩票配置数据到客户端: " .. src)
        DebugPrint("配置数据结构: " .. json.encode(configData))

        if configData.double_ball then
            DebugPrint("双色球价格: " .. configData.double_ball.price)
            if configData.double_ball.prizes then
                local count = 0
                for _ in pairs(configData.double_ball.prizes) do
                    count = count + 1
                end
                DebugPrint("双色球奖项数量: " .. count)
            end
        end

        if configData.super_lotto then
            DebugPrint("大乐透价格: " .. configData.super_lotto.price)
            if configData.super_lotto.prizes then
                local count = 0
                for _ in pairs(configData.super_lotto.prizes) do
                    count = count + 1
                end
                DebugPrint("大乐透奖项数量: " .. count)
            end
        end

        -- 发送彩票配置数据
        DebugPrint("正在发送配置数据到客户端...")
        TriggerClientEvent('lottery:nui:receiveLotteryConfig', src, {
            config = configData
        })
        DebugPrint("配置数据已发送完成")
    end)
    
    -- 保存彩票配置
    RegisterNetEvent('lottery:saveLotteryConfig')
    AddEventHandler('lottery:saveLotteryConfig', function(data)
        SaveLotteryConfig(source, data)
    end)
    
    -- 保存所有彩票配置
    RegisterNetEvent('lottery:saveAllLotteryConfig')
    AddEventHandler('lottery:saveAllLotteryConfig', function(data)
        SaveAllLotteryConfig(source, data)
    end)
    
    -- 保存彩票奖项配置
    RegisterNetEvent('lottery:savePrizeConfig')
    AddEventHandler('lottery:savePrizeConfig', function(data)
        SavePrizeConfig(source, data)
    end)
    
    -- 保存刮刮乐配置和权重配置
    RegisterNetEvent('lottery:saveScratchRates')
    AddEventHandler('lottery:saveScratchRates', function(data)
        SaveScratchRates(source, data)
    end)
    
    -- 保存开奖设置
    RegisterNetEvent('lottery:saveDrawSettings')
    AddEventHandler('lottery:saveDrawSettings', function(data)
        SaveDrawSettings(source, data)
    end)
    
    -- 兼容旧版接口，重定向到新的处理函数
    RegisterNetEvent('lottery:saveScratchConfig')
    AddEventHandler('lottery:saveScratchConfig', function(data)
        -- 转换为新格式
        local newData = {
            type = data.type,
            ratesType = 'config',
            rates = {
                price = data.price,
                maxPrize = data.maxPrize
            }
        }
        SaveScratchRates(source, newData)
    end)
    
    -- 账户管理
    RegisterNetEvent('lottery:depositToAccount')
    AddEventHandler('lottery:depositToAccount', function(amount)
        DepositToShopAccount(source, amount)
    end)
    
    -- 添加刷新缓存的事件
    RegisterNetEvent('lottery:refreshCache')
    AddEventHandler('lottery:refreshCache', function(cacheKey)
        local src = source
        local player = GetPlayerFromId(src)
        
        if not player or not IsPlayerAdmin(player) then
            TriggerClientEvent('lottery:notification', src, '错误', '您没有权限刷新缓存', 'error')
            return
        end
        
        if cacheKey then
            -- 刷新指定缓存
            ClearCache(cacheKey)
            TriggerClientEvent('lottery:notification', src, '成功', '已刷新' .. cacheKey .. '缓存', 'success')
        else
            -- 刷新所有缓存
            ClearAllCache()
            TriggerClientEvent('lottery:notification', src, '成功', '已刷新所有缓存', 'success')
        end
        
        -- 通知客户端刷新数据
        TriggerClientEvent('lottery:notification', src, '提示', '正在重新加载数据，请稍候...', 'info')
        -- 强制客户端重新加载数据
        TriggerClientEvent('lottery:forceRefresh', src)
    end)
    
    -- 添加获取职业等级的事件
    RegisterNetEvent('lottery:getJobGrades')
    AddEventHandler('lottery:getJobGrades', function()
        local src = source
        local player = GetPlayerFromId(src)
        
        if not player or not IsPlayerAdmin(player) then
            TriggerClientEvent('lottery:notification', src, '错误', '您没有权限获取职业等级信息', 'error')
            return
        end
        
        GetJobGrades(function(jobGrades)
            TriggerClientEvent('lottery:receiveJobGrades', src, jobGrades)
        end)
    end)
    
    -- 存取款
    RegisterNetEvent('lottery:manageAccount')
    AddEventHandler('lottery:manageAccount', function(action, amount)
        local src = source
        local player = GetPlayerFromId(src)
        
        if not player then return end
        if not IsPlayerAdmin(player) then
            TriggerClientEvent('lottery:notification', src, '错误', '您没有权限操作账户', 'error')
            return
        end
        
        if action == 'deposit' then
            -- 存款
            DepositToShopAccount(src, player, amount)
        elseif action == 'withdraw' then
            -- 取款
            WithdrawFromShopAccount(src, player, amount)
        end
    end)

    -- 更新账户余额
    RegisterNetEvent('lottery:updateAccountBalance')
    AddEventHandler('lottery:updateAccountBalance', function(amount, isDeposit, accountData, success, playerCash, transactions)
        local src = source
        -- 如果服务器发送了完整的账户数据，则直接使用
        if accountData then
            -- 更新本地缓存的账户数据
            if adminData and adminData.account then
                adminData.account = accountData
                
                -- 如果服务器发送了玩家现金数据，也更新它
                if playerCash ~= nil then
                    if not adminData.playerData then
                        adminData.playerData = {}
                    end
                    adminData.playerData.money = playerCash
                    DebugPrint("^2[彩票系统] ^7更新玩家现金数据: " .. playerCash)
                end
            end
            
            -- 获取最新交易记录
            GetTransactions(100, function(latestTransactions)
                -- 使用传入的交易记录或获取的最新交易记录
                local transactionsToSend = transactions or latestTransactions
                
                -- 立即发送更新信息到前端，传递操作成功状态、玩家现金和最新交易记录
                SendNUIMessage({
                    action = 'updateAccountBalance',
                    account = accountData,
                    success = success ~= false, -- 如果success未指定，默认为true
                    playerCash = playerCash,
                    transactions = transactionsToSend
                })
                
                -- 只有在操作成功时才显示成功提示
                if amount and amount > 0 and success ~= false then
                    local actionText = isDeposit and '存入' or '取出'
                    TriggerEvent('lottery:notification', '操作成功', '成功' .. actionText .. ' ¥' .. amount .. (isDeposit and ' 到' or ' 从') .. '彩票店账户', 'success')
                end
            end)
        else
            -- 兼容旧版本，如果没有接收到完整账户数据，则使用旧的更新方式
            if adminData and adminData.account then
                -- 只有在操作成功时才更新本地数据
                if success ~= false then
                    if isDeposit then
                        adminData.account.balance = adminData.account.balance + amount
                        adminData.account.total_income = adminData.account.total_income + amount
                    else
                        adminData.account.balance = adminData.account.balance - amount
                        adminData.account.total_payout = adminData.account.total_payout + amount
                    end
                    
                    -- 如果服务器发送了玩家现金数据，也更新它
                    if playerCash ~= nil then
                        if not adminData.playerData then
                            adminData.playerData = {}
                        end
                        adminData.playerData.money = playerCash
                        DebugPrint("^2[彩票系统] ^7更新玩家现金数据: " .. playerCash)
                    end
                    
                end
                
                -- 获取最新交易记录
                GetTransactions(100, function(latestTransactions)
                    -- 使用传入的交易记录或获取的最新交易记录
                    local transactionsToSend = transactions or latestTransactions
                    
                    -- 立即发送更新信息到前端
                    SendNUIMessage({
                        action = 'updateAccountBalance',
                        account = adminData.account,
                        success = success ~= false, -- 如果success未指定，默认为true
                        playerCash = playerCash,
                        transactions = transactionsToSend
                    })
                    
                    -- 只有在操作成功时才显示成功提示
                    if success ~= false then
                        local actionText = isDeposit and '存入' or '取出'
                        TriggerEvent('lottery:notification', '操作成功', '成功' .. actionText .. ' ¥' .. amount .. (isDeposit and ' 到' or ' 从') .. '彩票店账户', 'success')
                    end
                    
                    -- 打印调试信息
                    DebugPrint("^2[彩票系统] ^7账户余额已更新:", 
                          "操作类型=" .. (isDeposit and "存款" or "取款"), 
                          "金额=" .. amount, 
                          "当前余额=" .. adminData.account.balance,
                          "操作成功=" .. (success ~= false and "是" or "否"),
                          "玩家现金=" .. (playerCash or "未知"),
                          "交易记录数=" .. #transactionsToSend)
                end)
            else
                DebugPrint("^1[彩票系统] ^7错误: 无法更新账户余额，账户数据不存在")
                
                -- 请求最新数据
                TriggerServerEvent('lottery:getAdminData')
            end
        end
        
        -- 清除相关缓存
        ClearCache("shopAccount")
        ClearCache("transactions")
    end)
    
    -- 员工管理相关事件
    
    -- 获取员工列表
    RegisterNetEvent('lottery:getEmployees')
    AddEventHandler('lottery:getEmployees', function()
        local src = source
        local player = GetPlayerFromId(src)
        
        -- 检查权限
        if not player or not IsPlayerAdmin(player) then
            TriggerClientEvent('lottery:notification', src, '错误', '您没有权限访问员工管理', 'error')
            return
        end
        
        -- 获取员工列表
        GetEmployeeList(function(employees)
            TriggerClientEvent('lottery:updateEmployeeList', src, {employees = employees})
        end)
    end)
    
    -- 招聘员工
    RegisterNetEvent('lottery:hireEmployee')
    AddEventHandler('lottery:hireEmployee', function(employeeId, employeeName, level, salary)
        local src = source
        local player = GetPlayerFromId(src)
        
        if not player or not IsPlayerAdmin(player) then
            TriggerClientEvent('lottery:notification', src, '错误', '您没有权限招聘员工', 'error')
            return
        end
        
        HireEmployee(player, employeeId, nil, level, salary)
    end)
    
    -- 解雇员工
    RegisterNetEvent('lottery:fireEmployee')
    AddEventHandler('lottery:fireEmployee', function(employeeId, reason)
        local src = source
        local player = GetPlayerFromId(src)
        
        if not player or not IsPlayerAdmin(player) then
            TriggerClientEvent('lottery:notification', src, '错误', '您没有权限解雇员工', 'error')
            return
        end
        
        FireEmployee(player, employeeId, reason)
    end)
    
    -- 更新员工等级
    RegisterNetEvent('lottery:updateEmployeeLevel')
    AddEventHandler('lottery:updateEmployeeLevel', function(employeeId, newLevel)
        local src = source
        local player = GetPlayerFromId(src)
        
        if not player or not IsPlayerAdmin(player) then
            TriggerClientEvent('lottery:notification', src, '错误', '您没有权限更新员工等级', 'error')
            return
        end
        
        UpdateEmployeeLevel(player, employeeId, newLevel)
    end)
    
    -- 更新员工薪资
    RegisterNetEvent('lottery:updateEmployeeSalary')
    AddEventHandler('lottery:updateEmployeeSalary', function(employeeId, newSalary)
        local src = source
        local player = GetPlayerFromId(src)
        
        if not player or not IsPlayerAdmin(player) then
            TriggerClientEvent('lottery:notification', src, '错误', '您没有权限更新员工薪资', 'error')
            return
        end
        
        UpdateEmployeeSalary(player, employeeId, newSalary)
    end)
    
    -- 发放员工薪资
    RegisterNetEvent('lottery:payEmployeeSalary')
    AddEventHandler('lottery:payEmployeeSalary', function()
        local src = source
        local player = GetPlayerFromId(src)
        
        if not player or not IsPlayerAdmin(player) then
            TriggerClientEvent('lottery:notification', src, '错误', '您没有权限发放薪资', 'error')
            return
        end
        
        PayEmployeeSalary(player)
    end)
    
    -- 获取员工日志
    RegisterNetEvent('lottery:getEmployeeLogs')
    AddEventHandler('lottery:getEmployeeLogs', function(employeeId)
        local src = source
        local player = GetPlayerFromId(src)
        
        if not player or not IsPlayerAdmin(player) then
            TriggerClientEvent('lottery:notification', src, '错误', '您没有权限查看员工日志', 'error')
            return
        end
        
        GetEmployeeLogs(employeeId, function(logs)
            TriggerClientEvent('lottery:receiveEmployeeLogs', src, employeeId, logs)
        end)
    end)
end

-- 检查玩家是否为管理员
function IsPlayerAdmin(player)
    -- 检查职业系统是否启用
    if Config.LotteryJob.enabled ~= true then
        return false
    end
    
    if Config.Framework == "ESX" then
        local job = player.getJob()
        -- 只有彩票店职业的人才能访问管理系统，管理员不行
        return job and job.name == Config.LotteryJob.name
    elseif Config.Framework == "QB" then
        local job = player.PlayerData.job
        -- 只有彩票店职业的人才能访问管理系统，管理员不行
        return job and job.name == Config.LotteryJob.name
    end
    return false
end

-- 获取玩家对象
function GetPlayerFromId(source)
    if Config.Framework == "ESX" then
        return Framework.GetPlayerFromId(source)
    elseif Config.Framework == "QB" then
        return Framework.Functions.GetPlayer(source)
    end
    return nil
end

-- 获取销售数据
function GetSalesData(cb)
    -- 检查是否有缓存
    if GetCachedData("salesData", false, cb) then
        DebugPrint("^2[彩票系统] ^7使用缓存的销售数据")
        return
    end
    
    -- 获取总销售数据
    DebugPrint("^3[彩票系统] ^7正在获取销售数据...")
    
    MySQL.Async.fetchAll([[
        SELECT 
            DATE(purchase_time) AS date,
            COUNT(*) AS tickets_sold,
            SUM(purchase_price) AS daily_revenue
        FROM scratch_cards
        GROUP BY DATE(purchase_time)
        ORDER BY date DESC
        LIMIT 30
    ]], {}, function(scratchResults)
        DebugPrint("^3[彩票系统] ^7刮刮乐销售数据获取完成，记录数: " .. #scratchResults)
        
        MySQL.Async.fetchAll([[
            SELECT 
                DATE(purchase_time) AS date,
                COUNT(*) AS tickets_sold,
                SUM(200) AS daily_revenue
            FROM lottery_tickets
            GROUP BY DATE(purchase_time)
            ORDER BY date DESC
            LIMIT 30
        ]], {}, function(lotteryResults)
            DebugPrint("^3[彩票系统] ^7彩票销售数据获取完成，记录数: " .. #lotteryResults)
            
            -- 合并结果
            local salesByDate = {}
            
            -- 处理刮刮乐数据
            for _, row in ipairs(scratchResults) do
                salesByDate[row.date] = {
                    date = row.date,
                    scratch_sold = row.tickets_sold or 0,
                    scratch_revenue = row.daily_revenue or 0,
                    lottery_sold = 0,
                    lottery_revenue = 0
                }
            end
            
            -- 处理彩票数据
            for _, row in ipairs(lotteryResults) do
                if salesByDate[row.date] then
                    salesByDate[row.date].lottery_sold = row.tickets_sold or 0
                    salesByDate[row.date].lottery_revenue = row.daily_revenue or 0
                else
                    salesByDate[row.date] = {
                        date = row.date,
                        scratch_sold = 0,
                        scratch_revenue = 0,
                        lottery_sold = row.tickets_sold or 0,
                        lottery_revenue = row.daily_revenue or 0
                    }
                end
            end
            
            -- 转换为数组
            local salesData = {}
            for _, data in pairs(salesByDate) do
                data.total_sold = (data.scratch_sold or 0) + (data.lottery_sold or 0)
                data.total_revenue = (data.scratch_revenue or 0) + (data.lottery_revenue or 0)
                table.insert(salesData, data)
            end
            
            -- 按日期排序
            table.sort(salesData, function(a, b)
                return a.date > b.date
            end)
            
            -- 计算总计
            local totalStats = {
                total_scratch_sold = 0,
                total_scratch_revenue = 0,
                total_lottery_sold = 0,
                total_lottery_revenue = 0
            }
            
            for _, data in ipairs(salesData) do
                totalStats.total_scratch_sold = totalStats.total_scratch_sold + (data.scratch_sold or 0)
                totalStats.total_scratch_revenue = totalStats.total_scratch_revenue + (data.scratch_revenue or 0)
                totalStats.total_lottery_sold = totalStats.total_lottery_sold + (data.lottery_sold or 0)
                totalStats.total_lottery_revenue = totalStats.total_lottery_revenue + (data.lottery_revenue or 0)
            end
            
            totalStats.total_sold = totalStats.total_scratch_sold + totalStats.total_lottery_sold
            totalStats.total_revenue = totalStats.total_scratch_revenue + totalStats.total_lottery_revenue
            
            -- 打印总计数据
            DebugPrint("^2[彩票系统] ^7销售数据统计:")
            DebugPrint("^2[彩票系统] ^7总销售量: " .. totalStats.total_sold)
            DebugPrint("^2[彩票系统] ^7总收入: " .. totalStats.total_revenue)
            DebugPrint("^2[彩票系统] ^7刮刮乐销量: " .. totalStats.total_scratch_sold)
            DebugPrint("^2[彩票系统] ^7彩票销量: " .. totalStats.total_lottery_sold)
            
            -- 缓存并返回结果
            SetCacheData("salesData", {
                daily = salesData,
                total = totalStats
            })
            
            cb({
                daily = salesData,
                total = totalStats
            })
        end)
    end)
end

-- 获取中奖记录
function GetWinningRecords(cb)
    -- 检查是否有缓存
    if GetCachedData("winningRecords", false, cb) then
        DebugPrint("^2[彩票系统] ^7使用缓存的中奖记录")
        return
    end
    
    MySQL.Async.fetchAll([[
        SELECT 
            id,
            player_name,
            card_type,
            card_name,
            prize_amount,
            DATE_FORMAT(purchase_time, '%Y-%m-%d %H:%i:%s') as purchase_time,
            DATE_FORMAT(scratch_time, '%Y-%m-%d %H:%i:%s') as scratch_time,
            DATE_FORMAT(claimed_time, '%Y-%m-%d %H:%i:%s') as claimed_time,
            is_claimed
        FROM scratch_cards
        WHERE prize_amount > 0
        ORDER BY scratch_time DESC
        LIMIT 100
    ]], {}, function(scratchResults)
        MySQL.Async.fetchAll([[
            SELECT 
                id,
                player_name,
                lottery_type,
                prize_level,
                prize_amount,
                DATE_FORMAT(purchase_time, '%Y-%m-%d %H:%i:%s') as purchase_time,
                DATE_FORMAT(draw_date, '%Y-%m-%d %H:%i:%s') as draw_date,
                DATE_FORMAT(claimed_time, '%Y-%m-%d %H:%i:%s') as claimed_time,
                is_claimed
            FROM lottery_tickets
            WHERE is_winning = 1
            ORDER BY draw_date DESC
            LIMIT 100
        ]], {}, function(lotteryResults)
            local result = {
                scratch = scratchResults or {},
                lottery = lotteryResults or {}
            }
            
            -- 缓存结果
            SetCacheData("winningRecords", result)
            
            -- 调用回调函数
            cb(result)
        end)
    end)
end

-- 获取兑奖记录
function GetClaimRecords(cb)
    -- 检查是否有缓存
    if GetCachedData("claimRecords", false, cb) then
        DebugPrint("^2[彩票系统] ^7使用缓存的兑奖记录")
        return
    end
    
    MySQL.Async.fetchAll([[
        SELECT 
            id,
            player_name,
            card_type,
            card_name,
            prize_amount,
            DATE_FORMAT(purchase_time, '%Y-%m-%d %H:%i:%s') as purchase_time,
            DATE_FORMAT(scratch_time, '%Y-%m-%d %H:%i:%s') as scratch_time,
            DATE_FORMAT(claimed_time, '%Y-%m-%d %H:%i:%s') as claimed_time
        FROM scratch_cards
        WHERE is_claimed = 1
        ORDER BY claimed_time DESC
        LIMIT 100
    ]], {}, function(scratchResults)
        MySQL.Async.fetchAll([[
            SELECT 
                id,
                player_name,
                lottery_type,
                prize_level,
                prize_amount,
                DATE_FORMAT(purchase_time, '%Y-%m-%d %H:%i:%s') as purchase_time,
                DATE_FORMAT(draw_date, '%Y-%m-%d %H:%i:%s') as draw_date,
                DATE_FORMAT(claimed_time, '%Y-%m-%d %H:%i:%s') as claimed_time
            FROM lottery_tickets
            WHERE is_claimed = 1
            ORDER BY claimed_time DESC
            LIMIT 100
        ]], {}, function(lotteryResults)
            local result = {
                scratch = scratchResults or {},
                lottery = lotteryResults or {}
            }
            
            -- 缓存结果
            SetCacheData("claimRecords", result)
            
            -- 调用回调函数
            cb(result)
        end)
    end)
end

-- 获取未兑奖记录
function GetUnclaimedRecords(cb)
    -- 检查是否有缓存
    if GetCachedData("unclaimedRecords", false, cb) then
        DebugPrint("^2[彩票系统] ^7使用缓存的未兑奖记录")
        return
    end
    
    MySQL.Async.fetchAll([[
        SELECT 
            id,
            player_name,
            card_type,
            card_name,
            prize_amount,
            DATE_FORMAT(purchase_time, '%Y-%m-%d %H:%i:%s') as purchase_time,
            DATE_FORMAT(scratch_time, '%Y-%m-%d %H:%i:%s') as scratch_time
        FROM scratch_cards
        WHERE prize_amount > 0 AND is_claimed = 0
        ORDER BY scratch_time DESC
        LIMIT 100
    ]], {}, function(scratchResults)
        MySQL.Async.fetchAll([[
            SELECT 
                id,
                player_name,
                lottery_type,
                prize_level,
                prize_amount,
                DATE_FORMAT(purchase_time, '%Y-%m-%d %H:%i:%s') as purchase_time,
                DATE_FORMAT(draw_date, '%Y-%m-%d %H:%i:%s') as draw_date
            FROM lottery_tickets
            WHERE is_winning = 1 AND is_claimed = 0
            ORDER BY draw_date DESC
            LIMIT 100
        ]], {}, function(lotteryResults)
            local result = {
                scratch = scratchResults or {},
                lottery = lotteryResults or {}
            }
            
            -- 缓存结果
            SetCacheData("unclaimedRecords", result)
            
            -- 调用回调函数
            cb(result)
        end)
    end)
end

-- 获取彩票店账户信息
function GetShopAccount(cb)
    -- 检查是否有缓存
    if GetCachedData("shopAccount", false, cb) then
        DebugPrint("^2[彩票系统] ^7使用缓存的账户信息")
        return
    end
    
    if Config.Framework == "ESX" then
        -- 使用esx_addonaccount替代直接数据库查询
        TriggerEvent('esx_addonaccount:getSharedAccount', 'society_lottery', function(societyAccount)
            if societyAccount then
                -- 创建账户数据结构
                local account = {
                    balance = societyAccount.money,
                    total_income = 0,  -- 这些字段在esx_addonaccount中没有，保持0以兼容现有UI
                    total_payout = 0,
                    last_updated = os.date("%Y/%m/%d %H:%M:%S")
                }
                
                -- 尝试从数据库获取历史交易总数据（如果需要）
                MySQL.Async.fetchAll('SELECT SUM(amount) as total_income FROM lottery_transactions WHERE transaction_type = "deposit" OR transaction_type = "sales"', {}, function(incomeResult)
                    if incomeResult and incomeResult[1] and incomeResult[1].total_income then
                        account.total_income = incomeResult[1].total_income
                    end
                    
                    MySQL.Async.fetchAll('SELECT SUM(ABS(amount)) as total_payout FROM lottery_transactions WHERE transaction_type = "withdraw" OR transaction_type = "prize_payout"', {}, function(payoutResult)
                        if payoutResult and payoutResult[1] and payoutResult[1].total_payout then
                            account.total_payout = payoutResult[1].total_payout
                        end
                        
                        -- 打印调试信息
                        DebugPrint("^2[彩票系统] ^7获取账户信息成功(esx_addonaccount): ", 
                            "余额=" .. account.balance, 
                            "总收入=" .. account.total_income, 
                            "总支出=" .. account.total_payout,
                            "更新时间=" .. account.last_updated)
                            
                        -- 缓存账户数据
                        SetCacheData("shopAccount", account)
                        cb(account)
                    end)
                end)
            else
                DebugPrint("^1[彩票系统] ^7警告: 未找到society_lottery账户")
                
                -- 返回默认账户数据
                local account = {
                    balance = 0,
                    total_income = 0,
                    total_payout = 0,
                    last_updated = os.date("%Y/%m/%d %H:%M:%S")
                }
                
                -- 缓存默认账户数据
                SetCacheData("shopAccount", account)
                cb(account)
            end
        end)
    else -- Config.Framework == "QB"
        -- 使用lottery_shop_accounts表
        MySQL.Async.fetchAll('SELECT * FROM lottery_shop_accounts WHERE id = 1', {}, function(result)
            if result and #result > 0 then
                local shopAccount = result[1]
                
                -- 创建账户数据结构
                local account = {
                    balance = shopAccount.balance,
                    total_income = shopAccount.total_income,
                    total_payout = shopAccount.total_payout,
                    last_updated = os.date("%Y/%m/%d %H:%M:%S", os.time(os.date('!*t', shopAccount.last_updated)))
                }
                
                -- 打印调试信息
                DebugPrint("^2[彩票系统] ^7获取账户信息成功(lottery_shop_accounts): ", 
                    "余额=" .. account.balance, 
                    "总收入=" .. account.total_income, 
                    "总支出=" .. account.total_payout,
                    "更新时间=" .. account.last_updated)
                    
                -- 缓存账户数据
                SetCacheData("shopAccount", account)
                cb(account)
            else
                DebugPrint("^1[彩票系统] ^7警告: 未找到彩票店账户信息")
                
                -- 返回默认账户数据
                local account = {
                    balance = 0,
                    total_income = 0,
                    total_payout = 0,
                    last_updated = os.date("%Y/%m/%d %H:%M:%S")
                }
                
                -- 缓存默认账户数据
                SetCacheData("shopAccount", account)
                cb(account)
            end
        end)
    end
end

-- 存款到彩票店账户
function DepositToShopAccount(src, player, amount)
    if not src or not player or not amount or amount <= 0 then
        if src then
            TriggerClientEvent('lottery:notification', src, '错误', '无效的金额', 'error')
        end
        return
    end
    
    -- 获取玩家现金
    local playerCash = GetPlayerMoney(src)
    
    -- 检查玩家现金是否足够
    if playerCash < amount then
        TriggerClientEvent('lottery:notification', src, '错误', '现金不足', 'error')
        TriggerClientEvent('lottery:updateAccountBalance', src, amount, true, nil, false, playerCash)
        return
    end
    
    -- 从玩家扣除现金
    if not RemovePlayerMoney(src, amount) then
        TriggerClientEvent('lottery:notification', src, '错误', '扣款失败', 'error')
        TriggerClientEvent('lottery:updateAccountBalance', src, amount, true, nil, false, playerCash)
        return
    end
    
    if Config.Framework == "ESX" then
        -- 使用esx_addonaccount API添加资金
        TriggerEvent('esx_addonaccount:getSharedAccount', 'society_lottery', function(societyAccount)
            if societyAccount then
                -- 添加资金到社团账户
                societyAccount.addMoney(amount)
                
                -- 记录交易明细
                local playerIdentifier = GetPlayerIdentifier(src)
                local playerName = GetPlayerName(src)
                LogTransaction('deposit', amount, '彩票店存款', playerIdentifier, playerName)
                
                -- 获取最新的账户数据
                GetShopAccount(function(accountData)
                    -- 获取最新的交易记录
                    GetTransactions(100, function(transactions)
                        -- 获取玩家最新现金
                        local updatedPlayerCash = GetPlayerMoney(src)
                        
                        -- 发送更新给客户端，包括最新的账户数据、交易记录和玩家现金
                        TriggerClientEvent('lottery:updateAccountBalance', src, amount, true, accountData, true, updatedPlayerCash, transactions)
                        
                        -- 记录管理操作
                        LogAdminAction(player, '存款', '向彩票店账户存款', amount)
                    end)
                end)
            else
                TriggerClientEvent('lottery:notification', src, '错误', '存款失败，彩票店账户不存在', 'error')
                -- 退还玩家现金
                AddPlayerMoney(src, amount)
                TriggerClientEvent('lottery:updateAccountBalance', src, amount, true, nil, false, GetPlayerMoney(src))
            end
        end)
    else -- Config.Framework == "QB"
        -- 使用lottery_shop_accounts表
        MySQL.Async.execute('UPDATE lottery_shop_accounts SET balance = balance + ?, total_income = total_income + ? WHERE id = 1', 
            {amount, amount}, 
            function(rowsChanged)
                if rowsChanged > 0 then
                    -- 记录交易明细
                    local playerIdentifier = GetPlayerIdentifier(src)
                    local playerName = GetPlayerName(src)
                    LogTransaction('deposit', amount, '彩票店存款', playerIdentifier, playerName)
                    
                    -- 获取最新的账户数据
                    GetShopAccount(function(accountData)
                        -- 获取最新的交易记录
                        GetTransactions(100, function(transactions)
                            -- 获取玩家最新现金
                            local updatedPlayerCash = GetPlayerMoney(src)
                            
                            -- 发送更新给客户端，包括最新的账户数据、交易记录和玩家现金
                            TriggerClientEvent('lottery:updateAccountBalance', src, amount, true, accountData, true, updatedPlayerCash, transactions)
                            
                            -- 记录管理操作
                            LogAdminAction(player, '存款', '向彩票店账户存款', amount)
                        end)
                    end)
                else
                    TriggerClientEvent('lottery:notification', src, '错误', '存款失败，无法更新彩票店账户', 'error')
                    -- 退还玩家现金
                    AddPlayerMoney(src, amount)
                    TriggerClientEvent('lottery:updateAccountBalance', src, amount, true, nil, false, GetPlayerMoney(src))
                end
            end
        )
    end
    
    -- 清除账户缓存
    ClearCache("shopAccount")
    ClearCache("transactions")
end

-- 从彩票店账户取款
function WithdrawFromShopAccount(src, player, amount)
    if not src or not player or not amount or amount <= 0 then
        if src then
            TriggerClientEvent('lottery:notification', src, '错误', '无效的金额', 'error')
        end
        return
    end
    
    if Config.Framework == "ESX" then
        -- 使用esx_addonaccount API获取账户
        TriggerEvent('esx_addonaccount:getSharedAccount', 'society_lottery', function(societyAccount)
            if not societyAccount then
                TriggerClientEvent('lottery:notification', src, '错误', '账户不存在', 'error')
                TriggerClientEvent('lottery:updateAccountBalance', src, amount, false, nil, false, GetPlayerMoney(src))
                return
            end
            
            -- 检查账户余额是否足够
            if societyAccount.money < amount then
                TriggerClientEvent('lottery:notification', src, '错误', '账户余额不足', 'error')
                
                -- 获取最新账户数据用于UI更新
                GetShopAccount(function(account) 
                    TriggerClientEvent('lottery:updateAccountBalance', src, amount, false, account, false, GetPlayerMoney(src))
                end)
                return
            end
            
            -- 尝试从账户扣款
            societyAccount.removeMoney(amount)
            
            -- 给玩家添加现金
            if AddPlayerMoney(src, amount) then
                -- 记录交易明细
                local playerIdentifier = GetPlayerIdentifier(src)
                local playerName = GetPlayerName(src)
                LogTransaction('withdraw', -amount, '彩票店取款', playerIdentifier, playerName)
                
                -- 获取最新的账户数据
                GetShopAccount(function(updatedAccount)
                    -- 获取最新的交易记录
                    GetTransactions(100, function(transactions)
                        -- 获取玩家最新现金
                        local updatedPlayerCash = GetPlayerMoney(src)
                        
                        -- 发送更新给客户端，包括最新的账户数据、交易记录和玩家现金
                        TriggerClientEvent('lottery:updateAccountBalance', src, amount, false, updatedAccount, true, updatedPlayerCash, transactions)
                        
                        -- 记录管理操作
                        LogAdminAction(player, '取款', '从彩票店账户取款', amount)
                    end)
                end)
            else
                -- 如果添加现金失败，恢复账户余额
                societyAccount.addMoney(amount)
                TriggerClientEvent('lottery:notification', src, '错误', '取款失败，无法添加现金', 'error')
                TriggerClientEvent('lottery:updateAccountBalance', src, amount, false, nil, false, GetPlayerMoney(src))
            end
        end)
    else -- Framework == "QB"
        -- 使用lottery_shop_accounts表
        MySQL.Async.fetchAll('SELECT balance FROM lottery_shop_accounts WHERE id = 1', {}, function(result)
            if not result or #result == 0 then
                TriggerClientEvent('lottery:notification', src, '错误', '账户不存在', 'error')
                TriggerClientEvent('lottery:updateAccountBalance', src, amount, false, nil, false, GetPlayerMoney(src))
                return
            end
            
            -- 检查账户余额是否足够
            local balance = result[1].balance
            if balance < amount then
                TriggerClientEvent('lottery:notification', src, '错误', '账户余额不足', 'error')
                
                -- 获取最新账户数据用于UI更新
                GetShopAccount(function(account) 
                    TriggerClientEvent('lottery:updateAccountBalance', src, amount, false, account, false, GetPlayerMoney(src))
                end)
                return
            end
            
            -- 给玩家添加现金
            if AddPlayerMoney(src, amount) then
                -- 尝试从账户扣款
                MySQL.Async.execute('UPDATE lottery_shop_accounts SET balance = balance - ?, total_payout = total_payout + ? WHERE id = 1 AND balance >= ?', 
                    {amount, amount, amount}, 
                    function(rowsChanged)
                        if rowsChanged > 0 then
                            -- 记录交易明细
                            local playerIdentifier = GetPlayerIdentifier(src)
                            local playerName = GetPlayerName(src)
                            LogTransaction('withdraw', -amount, '彩票店取款', playerIdentifier, playerName)
                            
                            -- 获取最新的账户数据
                            GetShopAccount(function(updatedAccount)
                                -- 获取最新的交易记录
                                GetTransactions(100, function(transactions)
                                    -- 获取玩家最新现金
                                    local updatedPlayerCash = GetPlayerMoney(src)
                                    
                                    -- 发送更新给客户端，包括最新的账户数据、交易记录和玩家现金
                                    TriggerClientEvent('lottery:updateAccountBalance', src, amount, false, updatedAccount, true, updatedPlayerCash, transactions)
                                    
                                    -- 记录管理操作
                                    LogAdminAction(player, '取款', '从彩票店账户取款', amount)
                                end)
                            end)
                        else
                            -- 如果数据库更新失败，恢复玩家现金
                            RemovePlayerMoney(src, amount)
                            TriggerClientEvent('lottery:notification', src, '错误', '取款失败，无法更新账户余额', 'error')
                            TriggerClientEvent('lottery:updateAccountBalance', src, amount, false, nil, false, GetPlayerMoney(src))
                        end
                    end
                )
            else
                TriggerClientEvent('lottery:notification', src, '错误', '取款失败，无法添加现金', 'error')
                TriggerClientEvent('lottery:updateAccountBalance', src, amount, false, nil, false, GetPlayerMoney(src))
            end
        end)
    end
    
    -- 清除账户缓存
    ClearCache("shopAccount")
    ClearCache("transactions")
end

-- 记录管理员操作
function LogAdminAction(player, action, details, amount)
    local identifier = nil
    local name = nil
    
    -- 检查player是否为数字(源ID)
    if type(player) == "number" then
        -- 转换为玩家对象
        player = GetPlayerFromId(player)
    end
    
    -- 如果player为nil或不是表，则使用默认值
    if player == nil or type(player) ~= "table" then
        identifier = "system"
        name = "系统"
    else
    if Config.Framework == "ESX" then
        identifier = player.identifier
        name = player.getName()
    elseif Config.Framework == "QB" then
        identifier = player.PlayerData.citizenid
        name = player.PlayerData.charinfo.firstname .. ' ' .. player.PlayerData.charinfo.lastname
        end
    end
    
    MySQL.Async.execute('INSERT INTO lottery_admin_logs (admin_id, admin_name, action, details, amount) VALUES (?, ?, ?, ?, ?)',
        {identifier, name, action, details, amount or 0}
    )
end

-- 记录交易明细
function LogTransaction(transactionType, amount, description, playerId, playerName)
    MySQL.Async.execute('INSERT INTO lottery_transactions (transaction_type, amount, description, player_id, player_name) VALUES (?, ?, ?, ?, ?)',
        {transactionType, amount, description, playerId, playerName}
    )
    
    -- 清除交易缓存
    ClearCache("transactions")
    -- 清除账户缓存
    ClearCache("shopAccount")
    
    DebugPrint(string.format("^2[彩票系统] ^7记录交易明细: 类型=%s, 金额=%d, 描述=%s, 玩家=%s", 
        transactionType, amount, description or "", playerName or ""))
end

-- 获取交易明细
function GetTransactions(limit, cb)
    -- 检查是否有缓存
    if GetCachedData("transactions", false, cb) then
        DebugPrint("^2[彩票系统] ^7使用缓存的交易明细")
        return
    end
    
    MySQL.Async.fetchAll('SELECT * FROM lottery_transactions ORDER BY timestamp DESC LIMIT ?', {limit}, function(result)
        -- 缓存交易明细
        SetCacheData("transactions", result or {})
        cb(result or {})
    end)
end

-- 获取玩家现金
function GetPlayerMoney(src)
    local player = GetPlayerFromId(src)
    if not player then return 0 end
    
    if Config.Framework == "ESX" then
        return player.getMoney() or 0
    elseif Config.Framework == "QB" then
        return player.PlayerData.money.cash or 0
    end
    return 0
end

-- 添加玩家现金
function AddPlayerMoney(src, amount)
    local player = GetPlayerFromId(src)
    if not player then return false end
    
    if Config.Framework == "ESX" then
        player.addMoney(amount)
        return true
    elseif Config.Framework == "QB" then
        player.Functions.AddMoney('cash', amount, '彩票店取款')
        return true
    end
    return false
end

-- 移除玩家现金
function RemovePlayerMoney(src, amount)
    local player = GetPlayerFromId(src)
    if not player then return false end
    
    if Config.Framework == "ESX" then
        if player.getMoney() >= amount then
            player.removeMoney(amount)
            return true
        end
    elseif Config.Framework == "QB" then
        if player.PlayerData.money.cash >= amount then
            player.Functions.RemoveMoney('cash', amount, '彩票店存款')
            return true
        end
    end
    return false
end

-- 获取玩家标识符
function GetPlayerIdentifier(src)
    local player = GetPlayerFromId(src)
    if not player then return nil end
    
    if Config.Framework == "ESX" then
        return player.identifier
    elseif Config.Framework == "QB" then
        return player.PlayerData.citizenid
    end
    return nil
end

-- 获取玩家姓名
function GetPlayerName(src)
    local player = GetPlayerFromId(src)
    if not player then return "未知玩家" end
    
    if Config.Framework == "ESX" then
        return player.getName()
    elseif Config.Framework == "QB" then
        return player.PlayerData.charinfo.firstname .. ' ' .. player.PlayerData.charinfo.lastname
    end
    return "未知玩家"
end

-- 员工管理相关函数 --

-- 获取所有员工
function GetAllEmployees(cb)
    -- 检查是否有缓存
    if GetCachedData("employees", false, cb) then
        DebugPrint("^2[彩票系统] ^7使用缓存的员工列表")
        return
    end
    
    -- 添加调试信息
    DebugPrint("^3[彩票系统] ^7正在获取员工列表...")
    
    -- 确保lottery_employees表存在
    MySQL.Async.fetchScalar('SHOW TABLES LIKE "lottery_employees"', {}, function(tableExists)
        if not tableExists then
            -- 表不存在，创建表
            DebugPrint("^1[彩票系统] ^7员工表不存在，正在创建...")
            MySQL.Async.execute([[
                CREATE TABLE IF NOT EXISTS lottery_employees (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    employee_id VARCHAR(50) NOT NULL UNIQUE,
                    employee_name VARCHAR(100) NOT NULL,
                    level INT DEFAULT 1,
                    salary INT DEFAULT 1000,
                    bonus INT DEFAULT 0,
                    hire_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_payment TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status ENUM('active', 'fired') DEFAULT 'active',
                    fee_commission_enabled TINYINT(1) DEFAULT 0,
                    fee_commission_rate FLOAT DEFAULT 0.1,
                    fee_commission_min_amount INT DEFAULT 500
                )
            ]], {}, function(success)
                if success then
                    DebugPrint("^2[彩票系统] ^7员工表创建成功")
                    -- 表创建成功后，同步职业玩家数据
                    SyncEmployeesFromUsers(cb)
                else
                    DebugPrint("^1[彩票系统] ^7员工表创建失败")
                    cb({})
                end
            end)
        else
            -- 表存在，先同步职业玩家数据，然后获取员工列表
            SyncEmployeesFromUsers(function()
                MySQL.Async.fetchAll('SELECT * FROM lottery_employees ORDER BY level DESC, hire_date ASC', {}, function(result)
                    if result then
                        DebugPrint("^2[彩票系统] ^7员工列表获取成功，共" .. #result .. "条记录")
                        -- 缓存员工列表
                        SetCacheData("employees", result)
                        cb(result)
                    else
                        DebugPrint("^1[彩票系统] ^7员工列表获取失败，返回空数组")
                        -- 缓存空数组
                        SetCacheData("employees", {})
                        cb({})
                    end
                end)
            end)
        end
    end)
end

-- 从users表同步彩票店职业的玩家到员工表
function SyncEmployeesFromUsers(cb)
    -- 清除员工缓存
    ClearCache("employees")
    
    DebugPrint("^3[彩票系统] ^7正在从users表同步彩票店职业玩家...")
    
    -- 根据框架不同，使用不同的查询语句
    local query = ""
    local jobName = Config.LotteryJob.name
    
    if Config.Framework == "ESX" then
        -- ESX框架，从users表获取彩票店职业的玩家
        query = [[
            SELECT 
                identifier as employee_id, 
                CONCAT(firstname, ' ', lastname) as employee_name,
                job_grade as level
            FROM users 
            WHERE job = ?
        ]]
    elseif Config.Framework == "QB" then
        -- QB框架，从players表获取彩票店职业的玩家
        query = [[
            SELECT 
                citizenid as employee_id, 
                CONCAT(JSON_EXTRACT(charinfo, '$.firstname'), ' ', JSON_EXTRACT(charinfo, '$.lastname')) as employee_name,
                JSON_EXTRACT(job, '$.grade.level') as level
            FROM players 
            WHERE JSON_EXTRACT(job, '$.name') = ?
        ]]
    else
        DebugPrint("^1[彩票系统] ^7未知的框架类型，无法同步员工数据")
        if cb then cb() end
        return
    end
    
    -- 执行查询 - 使用同步方式获取玩家数据，避免嵌套回调
    local players = MySQL.Sync.fetchAll(query, {jobName})
    
    if not players or #players == 0 then
        DebugPrint("^3[彩票系统] ^7未找到彩票店职业的玩家")
        if cb then cb() end
        return
    end
    
    DebugPrint("^2[彩票系统] ^7找到" .. #players .. "名彩票店职业玩家，开始同步到员工表")
    
    -- 使用事务批量处理更新，而不是一个接一个地处理
    MySQL.Async.transaction({
        {
            query = "START TRANSACTION"
        }
    }, function(success)
        if not success then
            DebugPrint("^1[彩票系统] ^7启动事务失败，同步中止")
            if cb then cb() end
            return
        end
        
        -- 对每个员工进行处理，但避免嵌套异步调用
        Citizen.CreateThread(function()
            local updatedCount = 0
            local insertedCount = 0
            local errorCount = 0
            
            for _, player in ipairs(players) do
                -- 清理数据
                player.employee_name = player.employee_name:gsub('"', '')
                player.level = tonumber(player.level) or 1
                
                -- 查询员工是否存在
                local result = MySQL.Sync.fetchAll('SELECT * FROM lottery_employees WHERE employee_id = ? FOR UPDATE', {player.employee_id})
                
                -- 使用延迟降低数据库负担
                Citizen.Wait(10)
                
                if result and #result > 0 then
                    -- 员工已存在，更新状态
                    local updateSuccess = MySQL.Sync.execute(
                        'UPDATE lottery_employees SET status = ?, level = ? WHERE employee_id = ?',
                        {'active', player.level, player.employee_id}
                    )
                    
                    if updateSuccess then
                        updatedCount = updatedCount + 1
                    else
                        errorCount = errorCount + 1
                        DebugPrint("^1[彩票系统] ^7更新员工失败: " .. player.employee_id)
                    end
                else
                    -- 员工不存在，添加新员工
                    local insertSuccess = MySQL.Sync.execute(
                        'INSERT INTO lottery_employees (employee_id, employee_name, level, salary, bonus, status, hire_date, fee_commission_enabled, fee_commission_rate, fee_commission_min_amount) VALUES (?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?)',
                        {player.employee_id, player.employee_name, player.level, 1000 + player.level * 500, 0, 'active', 0, 0.1, 500}
                    )
                    
                    if insertSuccess then
                        insertedCount = insertedCount + 1
                    else
                        errorCount = errorCount + 1
                        DebugPrint("^1[彩票系统] ^7添加员工失败: " .. player.employee_id)
                    end
                end
                
                -- 使用延迟降低数据库负担
                Citizen.Wait(10)
            end
            
            -- 提交事务
            local commitSuccess = MySQL.Sync.execute("COMMIT")
            
            if commitSuccess then
                DebugPrint(string.format("^2[彩票系统] ^7员工同步完成: %d 更新, %d 新增, %d 错误", 
                    updatedCount, insertedCount, errorCount))
            else
                DebugPrint("^1[彩票系统] ^7提交事务失败，可能需要重试")
                -- 尝试回滚事务
                MySQL.Sync.execute("ROLLBACK")
            end
            
            if cb then cb() end
        end)
    end)
end

-- 确保员工日志表存在
Citizen.CreateThread(function()
    Citizen.Wait(5000) -- 等待5秒确保MySQL连接已建立
    
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS lottery_employee_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_id VARCHAR(50) NOT NULL,
            employee_name VARCHAR(100) NOT NULL,
            action VARCHAR(50) NOT NULL,
            details TEXT,
            admin_id VARCHAR(50),
            admin_name VARCHAR(100),
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ]], {}, function(success)
        if success then
            DebugPrint("^2[彩票系统] ^7员工日志表检查/创建成功")
        else
            DebugPrint("^1[彩票系统] ^7员工日志表检查/创建失败")
        end
    end)
end)

-- 获取单个员工信息
function GetEmployee(employeeId, cb)
    MySQL.Async.fetchAll('SELECT * FROM lottery_employees WHERE employee_id = ?', {employeeId}, function(result)
        if result and result[1] then
            cb(result[1])
        else
            cb(nil)
        end
    end)
end

-- 添加一个通用的函数，用于在操作后刷新员工列表
function RefreshEmployeeList(adminPlayer)
    -- 获取最新员工列表
    GetAllEmployees(function(employees)
        -- 发送更新的员工列表给客户端
        TriggerClientEvent('lottery:updateEmployeeList', adminPlayer.source, employees)
        
        -- 添加一个特殊事件，通知客户端需要完全刷新界面
        Citizen.SetTimeout(100, function() -- 短暂延迟确保updateEmployeeList事件先处理
            TriggerClientEvent('lottery:refreshEmployeeUI', adminPlayer.source)
        end)
    end)
end

-- 获取玩家对象通过游戏内服务器ID
function GetPlayerByServerId(serverId)
    -- 转换为数字确保兼容性
    serverId = tonumber(serverId)
    if not serverId then
        DebugPrint("^1[彩票系统] ^7验证玩家ID失败：ID不是有效的数字")
        return nil, nil, "无效的ID"
    end
    
    DebugPrint("^3[彩票系统] ^7验证玩家ID: " .. serverId)
    
    -- 检查玩家是否在线（先尝试原生方法）
    local playerName = GetPlayerName(serverId)
    if not playerName or playerName == "" then
        DebugPrint("^1[彩票系统] ^7GetPlayerName返回空，玩家可能不在线: " .. serverId)
        
        -- 使用备用方法检查玩家是否在线
        local playerFound = false
        for _, id in ipairs(GetPlayers()) do
            if tonumber(id) == serverId then
                playerFound = true
                playerName = GetPlayerName(id) or "未知玩家"
                DebugPrint("^2[彩票系统] ^7通过GetPlayers找到玩家: " .. id .. ", 名称: " .. playerName)
                break
            end
        end
        
        if not playerFound then
            DebugPrint("^1[彩票系统] ^7未找到ID为 " .. serverId .. " 的玩家，确认玩家不在线")
            return nil, nil, "未找到ID为 " .. serverId .. " 的玩家，请确认玩家在线"
        end
    else
        DebugPrint("^2[彩票系统] ^7找到玩家名称: " .. playerName .. " (ID: " .. serverId .. ")")
    end
    
    -- 根据框架获取玩家对象
    local player = nil
    local playerIdentifier = nil
    
    if Config.Framework == "ESX" then
        DebugPrint("^3[彩票系统] ^7使用ESX框架获取玩家数据")
        if Framework.GetPlayerFromId then
            player = Framework.GetPlayerFromId(serverId)
            if player then
                playerName = player.getName and player.getName() or playerName
                playerIdentifier = player.identifier or player.getIdentifier and player.getIdentifier()
                DebugPrint("^2[彩票系统] ^7成功获取ESX玩家数据: " .. playerName)
            else
                DebugPrint("^1[彩票系统] ^7ESX.GetPlayerFromId返回nil")
            end
        end
    elseif Config.Framework == "QB" then
        DebugPrint("^3[彩票系统] ^7使用QB框架获取玩家数据")
        if Framework.Functions and Framework.Functions.GetPlayer then
            player = Framework.Functions.GetPlayer(serverId)
            if player and player.PlayerData then
                playerName = player.PlayerData.charinfo.firstname .. ' ' .. player.PlayerData.charinfo.lastname
                playerIdentifier = player.PlayerData.citizenid
                DebugPrint("^2[彩票系统] ^7成功获取QB玩家数据: " .. playerName)
            else
                DebugPrint("^1[彩票系统] ^7QB.Functions.GetPlayer返回无效数据")
            end
        end
    else
        DebugPrint("^3[彩票系统] ^7使用通用方法获取玩家数据")
        -- 通用方法，至少尝试获取名称
        playerIdentifier = "user_" .. serverId -- 临时标识符
    end
    
    if not playerIdentifier then
        DebugPrint("^3[彩票系统] ^7尝试使用原生方法获取标识符")
        -- 尝试使用原生方法获取标识符
        local identifiers = GetPlayerIdentifiers(serverId)
        if identifiers and #identifiers > 0 then
            DebugPrint("^2[彩票系统] ^7找到 " .. #identifiers .. " 个标识符")
            -- 优先使用license标识符
            for _, id in ipairs(identifiers) do
                DebugPrint("^3[彩票系统] ^7检查标识符: " .. id)
                if string.find(id, "license:") then
                    playerIdentifier = string.gsub(id, "license:", "")
                    DebugPrint("^2[彩票系统] ^7使用license标识符: " .. playerIdentifier)
                    break
                end
            end
            -- 如果没找到license，使用第一个标识符
            if not playerIdentifier then
                playerIdentifier = identifiers[1]
                DebugPrint("^3[彩票系统] ^7使用第一个标识符: " .. playerIdentifier)
            end
        else
            DebugPrint("^1[彩票系统] ^7无法获取玩家标识符")
        end
    end
    
    -- 只要我们有名称和标识符，就算成功，不强制需要player对象
    if not playerIdentifier then
        DebugPrint("^1[彩票系统] ^7验证失败：无法获取玩家标识符")
        return nil, nil, "找不到玩家数据，无法获取标识符"
    end
    
    DebugPrint("^2[彩票系统] ^7验证成功: ID=" .. serverId .. ", 名称=" .. playerName .. ", 标识符=" .. playerIdentifier)
    return player, playerName, playerIdentifier
end

-- 招聘员工
function HireEmployee(adminPlayer, serverId, employeeName, level, salary)
    -- 验证是否提供了玩家ID
    if not serverId then
        TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '必须提供玩家ID', 'error')
        return
    end
    
    -- 通过服务器ID获取玩家信息
    local player, playerName, playerIdentifier = GetPlayerByServerId(serverId)
    
    if not player then
        TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', playerIdentifier or '找不到该ID的玩家', 'error')
        return
    end
    
    if not playerIdentifier then
        TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '无法获取玩家标识符', 'error')
        return
    end
    
    -- 使用获取到的真实玩家姓名
    employeeName = playerName
    
    -- 确保level和salary是数字
    level = tonumber(level) or 0
    salary = tonumber(salary) or 1000
    
    -- 打印调试信息
    DebugPrint("^2[彩票系统] ^7尝试招聘员工: ID=" .. serverId .. ", 名称=" .. employeeName .. ", 标识符=" .. playerIdentifier .. ", 等级=" .. level .. ", 薪资=" .. salary)
    
    -- 使用同步查询获取员工信息，避免回调嵌套
    local existingEmployee = MySQL.Sync.fetchSingle('SELECT * FROM lottery_employees WHERE employee_id = ? FOR UPDATE', {playerIdentifier})
    
    -- 使用事务处理所有数据库操作
    MySQL.Async.transaction({
        {
            query = "START TRANSACTION"
        }
    }, function(success)
        if not success then
            TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '数据库事务启动失败', 'error')
            return
        end
        
        Citizen.CreateThread(function()
            local operationSuccess = false
            local updatedEmployee = nil
            
            if existingEmployee then
                if existingEmployee.status == 'fired' then
                    -- 如果是已解雇的员工，可以重新启用
                    local rowsChanged = MySQL.Sync.execute(
                        'UPDATE lottery_employees SET status = ?, level = ?, salary = ? WHERE employee_id = ?',
                        {'active', level, salary, playerIdentifier}
                    )
                    
                    if rowsChanged > 0 then
                        -- 记录员工日志
                        LogEmployeeAction(playerIdentifier, employeeName, 'rehire', '重新雇佣员工，等级: ' .. level .. ', 薪资: ' .. salary, adminPlayer)
                        
                        -- 设置玩家职业
                        SetPlayerJob(playerIdentifier, level)
                        
                        -- 创建更新后的员工对象用于UI更新
                        updatedEmployee = existingEmployee
                        updatedEmployee.status = 'active'
                        updatedEmployee.level = level
                        updatedEmployee.salary = salary
                        
                        operationSuccess = true
                    end
                else
                    -- 提交事务
                    MySQL.Sync.execute("COMMIT")
                    TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '该员工ID已存在', 'error')
                    return
                end
            else
                -- 创建新员工
                local rowsChanged = MySQL.Sync.execute(
                    'INSERT INTO lottery_employees (employee_id, employee_name, salary, level, status, hire_date) VALUES (?, ?, ?, ?, ?, NOW())',
                    {playerIdentifier, employeeName, salary, level, 'active'}
                )
                
                if rowsChanged > 0 then
                    -- 记录员工日志
                    LogEmployeeAction(playerIdentifier, employeeName, 'hire', '雇佣新员工，等级: ' .. level .. ', 薪资: ' .. salary, adminPlayer)
                    
                    -- 设置玩家职业
                    SetPlayerJob(playerIdentifier, level)
                    
                    -- 获取当前时间作为雇佣日期
                    local hireDate = os.date('%Y-%m-%d %H:%M:%S')
                    
                    -- 创建新员工对象用于UI更新
                    updatedEmployee = {
                        employee_id = playerIdentifier,
                        employee_name = employeeName,
                        salary = salary,
                        level = level,
                        status = 'active',
                        hire_date = hireDate,
                        last_payment = nil
                    }
                    
                    operationSuccess = true
                    DebugPrint("^2[彩票系统] ^7创建新员工成功: " .. employeeName)
                end
            end
            
            -- 提交事务
            local commitSuccess = MySQL.Sync.execute("COMMIT")
            
            if not commitSuccess then
                DebugPrint("^1[彩票系统] ^7提交事务失败")
                MySQL.Sync.execute("ROLLBACK")
                TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '数据库操作失败', 'error')
                return
            end
            
            if operationSuccess and updatedEmployee then
                -- 直接更新客户端UI
                if existingEmployee and existingEmployee.status == 'fired' then
                    TriggerClientEvent('lottery:directEmployeeUpdate', adminPlayer.source, {
                        type = 'rehire',
                        employee = updatedEmployee
                    })
                    
                    TriggerClientEvent('lottery:notification', adminPlayer.source, '操作成功', '已重新雇佣员工: ' .. employeeName, 'success')
                else
                    TriggerClientEvent('lottery:directEmployeeUpdate', adminPlayer.source, {
                        type = 'new',
                        employee = updatedEmployee
                    })
                    
                    TriggerClientEvent('lottery:notification', adminPlayer.source, '操作成功', '已雇佣新员工: ' .. employeeName, 'success')
                end
                
                -- 延迟一段时间再更新完整员工列表，确保直接更新先显示
                Citizen.SetTimeout(200, function()
                    GetAllEmployees(function(employees)
                        TriggerClientEvent('lottery:completeEmployeeData', adminPlayer.source, employees)
                    end)
                end)
            else
                TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '雇佣员工失败', 'error')
            end
        end)
    end)
    
    -- 清除员工缓存
    ClearCache("employees")
end

-- 设置玩家职业为彩票店职业
function SetPlayerJob(playerId, level)
    DebugPrint("^3[彩票系统] ^7尝试设置玩家 " .. playerId .. " 的职业为彩票店职业，等级: " .. level)
    
    -- 检查Framework是否已初始化
    if Framework == nil then
        DebugPrint("^1[彩票系统] ^7错误: Framework未初始化，无法设置玩家职业")
        -- 尝试直接更新数据库
        if Config.Framework == "ESX" then
            MySQL.Async.execute('UPDATE users SET job = ?, job_grade = ? WHERE identifier = ?',
                {Config.LotteryJob.name, level, playerId},
                function(rowsChanged)
                    if rowsChanged > 0 then
                        DebugPrint("^2[彩票系统] ^7成功更新数据库中玩家 " .. playerId .. " 的职业为彩票店职业")
                    else
                        DebugPrint("^1[彩票系统] ^7更新数据库失败，可能找不到玩家: " .. playerId)
                    end
                end
            )
        elseif Config.Framework == "QB" then
            DebugPrint("^1[彩票系统] ^7QB框架需要完整的job对象，无法直接更新数据库")
        end
        return
    end
    
    -- 根据框架不同，使用不同的方法设置职业
    if Config.Framework == "ESX" then
        -- ESX框架
        local player = nil
        local found = false
        
        -- 使用ESX Legacy方法获取在线玩家
        DebugPrint("^3[彩票系统] ^7使用ESX Legacy方法 (GetExtendedPlayers)")
        if Framework.GetExtendedPlayers then
            local players = Framework.GetExtendedPlayers()
            for _, xPlayer in pairs(players) do
                local id = xPlayer.identifier or xPlayer.getIdentifier()
                DebugPrint("^3[彩票系统] ^7检查玩家: " .. id)
                if id == playerId then
                    player = xPlayer
                    found = true
                    DebugPrint("^2[彩票系统] ^7成功通过GetExtendedPlayers获取玩家")
                    break
                end
            end
        else
            DebugPrint("^3[彩票系统] ^7ESX Legacy方法不可用，尝试直接更新数据库")
        end
        
        if player then
            -- 玩家在线，直接使用Framework API设置职业
            DebugPrint("^2[彩票系统] ^7玩家在线，直接使用Framework API设置职业")
            
            -- 检查setJob方法
            if player.setJob then
                player.setJob(Config.LotteryJob.name, level)
                DebugPrint("^2[彩票系统] ^7使用player.setJob方法")
                
                -- 同时直接更新数据库，确保数据同步
                MySQL.Async.execute('UPDATE users SET job = ?, job_grade = ? WHERE identifier = ?',
                    {Config.LotteryJob.name, level, playerId},
                    function(rowsChanged)
                        DebugPrint("^2[彩票系统] ^7同步更新数据库结果: " .. rowsChanged)
                    end
                )
            else
                DebugPrint("^1[彩票系统] ^7player.setJob方法不存在")
                -- 尝试直接更新数据库
                MySQL.Async.execute('UPDATE users SET job = ?, job_grade = ? WHERE identifier = ?',
                    {Config.LotteryJob.name, level, playerId},
                    function(rowsChanged)
                        DebugPrint("^3[彩票系统] ^7直接更新数据库结果: " .. rowsChanged)
                    end
                )
            end
            
            -- 获取玩家source
            local source = 0
            if player.source then
                source = player.source
            elseif player.getSource then
                source = player.getSource()
            end
            
            if source > 0 then
                TriggerClientEvent('lottery:notification', source, '通知', '您的职业已更新为彩票店员工，等级: ' .. level, 'info')
            end
            
            DebugPrint("^2[彩票系统] ^7成功设置在线玩家 " .. playerId .. " 的职业为彩票店职业")
        else
            -- 玩家离线，更新数据库
            DebugPrint("^3[彩票系统] ^7玩家离线或无法获取在线玩家对象，更新数据库")
            MySQL.Async.execute('UPDATE users SET job = ?, job_grade = ? WHERE identifier = ?',
                {Config.LotteryJob.name, level, playerId},
                function(rowsChanged)
                    if rowsChanged > 0 then
                        DebugPrint("^2[彩票系统] ^7成功设置离线玩家 " .. playerId .. " 的职业为彩票店职业")
                    else
                        DebugPrint("^1[彩票系统] ^7设置玩家职业失败，可能找不到玩家: " .. playerId)
                    end
                end
            )
        end
    elseif Config.Framework == "QB" then
        -- QB框架
        local player = nil
        
        -- 使用QB方法获取在线玩家
        if Framework.Functions and type(Framework.Functions.GetPlayerByCitizenId) == "function" then
            player = Framework.Functions.GetPlayerByCitizenId(playerId)
            if player then
                DebugPrint("^2[彩票系统] ^7成功通过GetPlayerByCitizenId获取玩家")
            end
        else
            DebugPrint("^1[彩票系统] ^7Framework.Functions.GetPlayerByCitizenId不是一个函数")
        end
        
        if player then
            -- 玩家在线，直接使用Framework API设置职业
            DebugPrint("^2[彩票系统] ^7玩家在线，直接使用Framework API设置职业")
            player.Functions.SetJob(Config.LotteryJob.name, level)
            
            -- 同时直接更新数据库，确保数据同步
            MySQL.Async.fetchAll('SELECT job FROM players WHERE citizenid = ?', {playerId}, function(result)
                if result and result[1] then
                    local jobData = json.decode(result[1].job)
                    if jobData then
                        -- 更新职业信息
                        jobData.name = Config.LotteryJob.name
                        jobData.label = Config.LotteryJob.label
                        jobData.payment = 0
                        
                        -- 设置职业等级
                        local gradeInfo = Config.LotteryJob.grades[level + 1] or Config.LotteryJob.grades[1]
                        jobData.grade = {
                            name = gradeInfo.name,
                            level = level
                        }
                        
                        -- 更新数据库
                        MySQL.Async.execute('UPDATE players SET job = ? WHERE citizenid = ?',
                            {json.encode(jobData), playerId},
                            function(rowsChanged)
                                DebugPrint("^2[彩票系统] ^7同步更新数据库结果: " .. rowsChanged)
                            end
                        )
                    end
                end
            end)
            
            TriggerClientEvent('lottery:notification', player.source, '通知', '您的职业已更新为彩票店员工，等级: ' .. level, 'info')
            DebugPrint("^2[彩票系统] ^7成功设置在线玩家 " .. playerId .. " 的职业为彩票店职业")
        else
            -- 玩家离线，更新数据库
            DebugPrint("^3[彩票系统] ^7玩家离线，更新数据库")
            MySQL.Async.fetchAll('SELECT job FROM players WHERE citizenid = ?', {playerId}, function(result)
                if result and result[1] then
                    local jobData = json.decode(result[1].job)
                    if jobData then
                        -- 更新职业信息
                        jobData.name = Config.LotteryJob.name
                        jobData.label = Config.LotteryJob.label
                        jobData.payment = 0
                        
                        -- 设置职业等级
                        local gradeInfo = Config.LotteryJob.grades[level + 1] or Config.LotteryJob.grades[1]
                        jobData.grade = {
                            name = gradeInfo.name,
                            level = level
                        }
                        
                        -- 更新数据库
                        MySQL.Async.execute('UPDATE players SET job = ? WHERE citizenid = ?',
                            {json.encode(jobData), playerId},
                            function(rowsChanged)
                                if rowsChanged > 0 then
                                    DebugPrint("^2[彩票系统] ^7成功设置离线玩家 " .. playerId .. " 的职业为彩票店职业")
                                else
                                    DebugPrint("^1[彩票系统] ^7设置离线玩家职业失败: " .. playerId)
                                end
                            end
                        )
                    end
                end
            end)
        end
    else
        DebugPrint("^1[彩票系统] ^7未知的框架类型，无法设置玩家职业")
    end
    
    -- 更新lottery_employees表中的level
    MySQL.Async.execute('UPDATE lottery_employees SET level = ? WHERE employee_id = ?',
        {level, playerId},
        function(rowsChanged)
            DebugPrint("^2[彩票系统] ^7更新员工表等级结果: " .. rowsChanged)
        end
    )
end

-- 解雇员工
function FireEmployee(adminPlayer, employeeId, reason)
    -- 使用同步查询获取员工信息
    local employee = MySQL.Sync.fetchSingle('SELECT * FROM lottery_employees WHERE employee_id = ? FOR UPDATE', {employeeId})
    
    if not employee then
        TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '员工不存在', 'error')
        return
    end
    
    if employee.status == 'fired' then
        TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '该员工已被解雇', 'error')
        return
    end
    
    -- 使用事务处理删除操作
    MySQL.Async.transaction({
        {
            query = "START TRANSACTION"
        }
    }, function(success)
        if not success then
            TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '数据库事务启动失败', 'error')
            return
        end
        
        Citizen.CreateThread(function()
            -- 从数据库中删除员工记录
            local rowsChanged = MySQL.Sync.execute('DELETE FROM lottery_employees WHERE employee_id = ?', {employeeId})
            
            if rowsChanged <= 0 then
                MySQL.Sync.execute("ROLLBACK")
                TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '解雇员工失败', 'error')
                return
            end
            
            -- 记录员工日志
            LogEmployeeAction(employeeId, employee.employee_name, 'fire', '解雇员工，原因: ' .. (reason or '无'), adminPlayer)
            
            -- 将玩家职业设置为unemployed
            SetUnemployedJob(employeeId)
            
            -- 提交事务
            local commitSuccess = MySQL.Sync.execute("COMMIT")
            
            if not commitSuccess then
                DebugPrint("^1[彩票系统] ^7提交事务失败")
                MySQL.Sync.execute("ROLLBACK")
                TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '数据库操作失败', 'error')
                return
            end
            
            -- 员工已删除，通知客户端删除此员工
            TriggerClientEvent('lottery:directEmployeeUpdate', adminPlayer.source, {
                type = 'delete',
                employeeId = employeeId
            })
            
            -- 通知客户端操作成功
            TriggerClientEvent('lottery:notification', adminPlayer.source, '操作成功', '已解雇并删除员工数据: ' .. employee.employee_name, 'success')
            
            -- 获取最新的完整员工列表
            Citizen.SetTimeout(200, function()
                GetAllEmployees(function(employees)
                    TriggerClientEvent('lottery:completeEmployeeData', adminPlayer.source, employees)
                end)
            end)
        end)
    end)
    
    -- 清除员工缓存
    ClearCache("employees")
end

-- 将玩家职业设置为unemployed
function SetUnemployedJob(playerId)
    DebugPrint("^3[彩票系统] ^7尝试将玩家 " .. playerId .. " 的职业设置为unemployed")
    
    -- 检查Framework是否已初始化
    if Framework == nil then
        DebugPrint("^1[彩票系统] ^7错误: Framework未初始化，无法设置玩家职业")
        -- 尝试直接更新数据库
        if Config.Framework == "ESX" then
            -- 使用同步操作避免回调嵌套
            local updateResult = MySQL.Sync.execute('UPDATE users SET job = ?, job_grade = ? WHERE identifier = ? AND job = ?',
                {'unemployed', 0, playerId, Config.LotteryJob.name}
            )
            
            if updateResult > 0 then
                DebugPrint("^2[彩票系统] ^7成功更新数据库中玩家 " .. playerId .. " 的职业为失业")
            else
                DebugPrint("^1[彩票系统] ^7更新数据库失败，可能找不到玩家或玩家不是彩票店职业: " .. playerId)
            end
        elseif Config.Framework == "QB" then
            DebugPrint("^1[彩票系统] ^7QB框架需要完整的job对象，无法直接更新数据库")
        end
        return
    end
    
    -- 以下代码保持基本不变，但对于数据库操作使用同步模式
    if Config.Framework == "ESX" then
        -- ESX框架
        local player = nil
        local found = false
        
        -- 使用ESX Legacy方法获取在线玩家
        DebugPrint("^3[彩票系统] ^7使用ESX Legacy方法 (GetExtendedPlayers)")
        if Framework.GetExtendedPlayers then
            local players = Framework.GetExtendedPlayers()
            for _, xPlayer in pairs(players) do
                local id = xPlayer.identifier or xPlayer.getIdentifier()
                if id == playerId then
                    player = xPlayer
                    found = true
                    DebugPrint("^2[彩票系统] ^7成功通过GetExtendedPlayers获取玩家")
                    break
                end
            end
        else
            DebugPrint("^3[彩票系统] ^7ESX Legacy方法不可用，尝试直接更新数据库")
        end
        
        if player then
            -- 玩家在线，直接使用Framework API设置职业
            DebugPrint("^2[彩票系统] ^7玩家在线，直接使用Framework API设置职业为unemployed")
            
            if player.setJob then
                player.setJob('unemployed', 0)
                DebugPrint("^2[彩票系统] ^7使用player.setJob方法")
                
                -- 同时直接更新数据库，确保数据同步
                local updateResult = MySQL.Sync.execute('UPDATE users SET job = ?, job_grade = ? WHERE identifier = ?',
                    {'unemployed', 0, playerId}
                )
                DebugPrint("^2[彩票系统] ^7同步更新数据库结果: " .. updateResult)
            else
                DebugPrint("^1[彩票系统] ^7player.setJob方法不存在")
                -- 尝试直接更新数据库
                local updateResult = MySQL.Sync.execute('UPDATE users SET job = ?, job_grade = ? WHERE identifier = ?',
                    {'unemployed', 0, playerId}
                )
                DebugPrint("^3[彩票系统] ^7直接更新数据库结果: " .. updateResult)
            end
            
            -- 获取玩家source
            local source = 0
            if player.source then
                source = player.source
            elseif player.getSource then
                source = player.getSource()
            end
            
            if source > 0 then
                TriggerClientEvent('lottery:notification', source, '通知', '您已被解雇，不再是彩票店员工', 'info')
            end
            
            DebugPrint("^2[彩票系统] ^7成功设置在线玩家 " .. playerId .. " 的职业为unemployed")
        else
            -- 玩家离线，更新数据库
            DebugPrint("^3[彩票系统] ^7玩家离线或无法获取在线玩家对象，更新数据库")
            local updateResult = MySQL.Sync.execute('UPDATE users SET job = ?, job_grade = ? WHERE identifier = ? AND job = ?',
                {'unemployed', 0, playerId, Config.LotteryJob.name}
            )
            
            if updateResult > 0 then
                DebugPrint("^2[彩票系统] ^7成功设置离线玩家 " .. playerId .. " 的职业为unemployed")
            else
                DebugPrint("^1[彩票系统] ^7设置玩家职业失败，可能找不到玩家或玩家不是彩票店职业: " .. playerId)
            end
        end
    elseif Config.Framework == "QB" then
        -- QB框架的部分大部分保持不变，因为它主要是使用框架API而不是直接访问数据库
        local player = nil
        
        -- 使用QB方法获取在线玩家
        if Framework.Functions and type(Framework.Functions.GetPlayerByCitizenId) == "function" then
            player = Framework.Functions.GetPlayerByCitizenId(playerId)
            if player then
                DebugPrint("^2[彩票系统] ^7成功通过GetPlayerByCitizenId获取玩家")
            end
        else
            DebugPrint("^1[彩票系统] ^7Framework.Functions.GetPlayerByCitizenId不是一个函数")
        end
        
        if player then
            -- 玩家在线，直接使用Framework API设置职业
            DebugPrint("^2[彩票系统] ^7玩家在线，直接使用Framework API设置职业")
            player.Functions.SetJob('unemployed', 0)
            
            TriggerClientEvent('lottery:notification', player.source, '通知', '您已被解雇，不再是彩票店员工', 'info')
            DebugPrint("^2[彩票系统] ^7成功设置在线玩家 " .. playerId .. " 的职业为unemployed")
        else
            -- 玩家离线，更新数据库 - 保持异步以维持原有逻辑
            DebugPrint("^3[彩票系统] ^7玩家离线，更新数据库")
            
            -- 但是添加错误处理
            MySQL.Async.fetchAll('SELECT job FROM players WHERE citizenid = ?', {playerId}, function(result)
                if result and result[1] then
                    local jobData = json.decode(result[1].job)
                    if jobData and jobData.name == Config.LotteryJob.name then
                        -- 更新为失业
                        jobData = {
                            name = 'unemployed',
                            label = '失业',
                            payment = 0,
                            grade = {
                                name = 'unemployed',
                                level = 0
                            }
                        }
                        
                        -- 添加重试机制
                        local tries = 0
                        local maxTries = 3
                        local function tryUpdate()
                            tries = tries + 1
                            MySQL.Async.execute('UPDATE players SET job = ? WHERE citizenid = ?',
                                {json.encode(jobData), playerId},
                                function(rowsChanged)
                                    if rowsChanged > 0 then
                                        DebugPrint("^2[彩票系统] ^7成功设置离线玩家 " .. playerId .. " 的职业为unemployed")
                                    else
                                        DebugPrint("^1[彩票系统] ^7更新数据库失败: " .. playerId)
                                        -- 如果仍有剩余尝试次数，则重试
                                        if tries < maxTries then
                                            DebugPrint("^3[彩票系统] ^7尝试重新更新，尝试 " .. tries .. "/" .. maxTries)
                                            Citizen.SetTimeout(100 * tries, tryUpdate)
                                        else
                                            DebugPrint("^1[彩票系统] ^7达到最大尝试次数，放弃更新")
                                        end
                                    end
                                end
                            )
                        end
                        
                        tryUpdate()
                    else
                        DebugPrint("^3[彩票系统] ^7玩家不是彩票店职业，无需更新")
                    end
                else
                    DebugPrint("^1[彩票系统] ^7找不到玩家记录: " .. playerId)
                end
            end)
        end
    end
end

-- 更新员工等级
function UpdateEmployeeLevel(adminPlayer, employeeId, newLevel)
    GetEmployee(employeeId, function(employee)
        if not employee then
            TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '员工不存在', 'error')
            return
        end
        
        if employee.status == 'fired' then
            TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '无法更新已解雇员工的等级', 'error')
            return
        end
        
        MySQL.Async.execute('UPDATE lottery_employees SET level = ? WHERE employee_id = ?',
            {newLevel, employeeId},
            function(rowsChanged)
                if rowsChanged > 0 then
                    -- 记录员工日志
                    LogEmployeeAction(employeeId, employee.employee_name, 'update_level', '更新员工等级，从 ' .. employee.level .. ' 到 ' .. newLevel, adminPlayer)
                    
                    -- 使用SetPlayerJob函数更新玩家职业等级
                    SetPlayerJob(employeeId, newLevel)
                    
                    -- 立即更新客户端界面的员工信息
                    local updatedEmployee = employee
                    updatedEmployee.level = newLevel
                    
                    -- 直接发送更新的员工信息给客户端
                    TriggerClientEvent('lottery:directEmployeeUpdate', adminPlayer.source, {
                        type = 'level',
                        employee = updatedEmployee
                    })
                    
                    -- 通知客户端操作成功
                    TriggerClientEvent('lottery:notification', adminPlayer.source, '操作成功', '已更新员工等级: ' .. employee.employee_name, 'success')
                    
                    -- 发送特定的等级更新事件，让客户端立即更新界面
                    TriggerClientEvent('lottery:refreshEmployeeLevel', adminPlayer.source, {
                        employeeId = employeeId,
                        newLevel = newLevel,
                        employeeName = employee.employee_name
                    })
                    
                    -- 获取最新的完整员工列表
                    Citizen.SetTimeout(100, function()
                        GetAllEmployees(function(employees)
                            TriggerClientEvent('lottery:completeEmployeeData', adminPlayer.source, employees)
                        end)
                    end)
                else
                    TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '更新员工等级失败', 'error')
                end
            end
        )
    end)
    
    -- 清除员工缓存
    ClearCache("employees")
end

-- 更新员工薪资
function UpdateEmployeeSalary(adminPlayer, employeeId, newSalary)
    GetEmployee(employeeId, function(employee)
        if not employee then
            TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '员工不存在', 'error')
            return
        end
        
        if employee.status == 'fired' then
            TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '无法更新已解雇员工的薪资', 'error')
            return
        end
        
        MySQL.Async.execute('UPDATE lottery_employees SET salary = ? WHERE employee_id = ?',
            {newSalary, employeeId},
            function(rowsChanged)
                if rowsChanged > 0 then
                    -- 记录员工日志
                    LogEmployeeAction(employeeId, employee.employee_name, 'update_salary', '更新员工薪资，从 ' .. employee.salary .. ' 到 ' .. newSalary, adminPlayer)
                    
                    -- 立即更新客户端界面的员工信息
                    local updatedEmployee = employee
                    updatedEmployee.salary = newSalary
                    
                    -- 直接发送更新的员工信息给客户端
                    TriggerClientEvent('lottery:directEmployeeUpdate', adminPlayer.source, {
                        type = 'salary',
                        employee = updatedEmployee
                    })
                    
                    -- 通知客户端操作成功
                    TriggerClientEvent('lottery:notification', adminPlayer.source, '操作成功', '已更新员工薪资: ' .. employee.employee_name, 'success')
                    
                    -- 发送特定的薪资更新事件，让客户端立即更新界面
                    TriggerClientEvent('lottery:refreshEmployeeSalary', adminPlayer.source, {
                        employeeId = employeeId,
                        newSalary = newSalary,
                        employeeName = employee.employee_name
                    })
                    
                    -- 获取最新的完整员工列表
                    Citizen.SetTimeout(100, function()
                        GetAllEmployees(function(employees)
                            TriggerClientEvent('lottery:completeEmployeeData', adminPlayer.source, employees)
                        end)
                    end)
                else
                    TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '更新员工薪资失败', 'error')
                end
            end
        )
    end)
    
    -- 清除员工缓存
    ClearCache("employees")
end

-- 发放员工薪资
function PayEmployeeSalary(adminPlayer)
    -- 获取所有活跃员工
    MySQL.Async.fetchAll('SELECT * FROM lottery_employees WHERE status = ?', {'active'}, function(employees)
        if not employees or #employees == 0 then
            TriggerClientEvent('lottery:notification', adminPlayer.source, '提示', '没有需要发放薪资的员工', 'info')
            return
        end
        
        -- 计算总薪资和奖金
        local totalSalary = 0
        local totalBonus = 0
        
        for _, employee in ipairs(employees) do
            totalSalary = totalSalary + employee.salary
            totalBonus = totalBonus + (employee.bonus or 0)
        end
        
        -- 修改：账户只扣除薪资，不扣除奖金
        local deductAmount = totalSalary
        
        -- 获取彩票店账户余额 (使用ESX账户系统)
        TriggerEvent('esx_addonaccount:getSharedAccount', 'society_lottery', function(societyAccount)
            if not societyAccount or societyAccount.money < deductAmount then
                TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '账户余额不足，无法发放薪资', 'error')
                return
            end
            
            -- 从社团账户扣除薪资总额
            societyAccount.removeMoney(deductAmount)
            
            -- 更新所有员工的最后发薪时间并清零奖金
            MySQL.Async.execute('UPDATE lottery_employees SET last_payment = CURRENT_TIMESTAMP, bonus = 0 WHERE status = ?',
                {'active'},
                function()
                    -- 记录交易明细 - 只记录薪资支出
                    LogTransaction('salary', -totalSalary, '员工薪资发放', GetPlayerIdentifier(adminPlayer.source), GetPlayerName(adminPlayer.source))
                    
                    -- 奖金记录为系统发放，不从账户扣除
                    if totalBonus > 0 then
                        LogTransaction('bonus', 0, '员工奖金发放(系统)', GetPlayerIdentifier(adminPlayer.source), GetPlayerName(adminPlayer.source))
                    end
                    
                    -- 记录每个员工的薪资和奖金发放
                    local onlineCount = 0
                    local offlineCount = 0
                    
                    for _, employee in ipairs(employees) do
                        local employeeBonus = employee.bonus or 0
                        local logDetails = '发放薪资: ' .. employee.salary
                        
                        if employeeBonus > 0 then
                            logDetails = logDetails .. ', 奖金: ' .. employeeBonus .. '(系统发放)'
                        end
                        
                        LogEmployeeAction(employee.employee_id, employee.employee_name, 'salary_bonus', logDetails, adminPlayer)
                        
                        -- 给员工发放薪资和奖金
                        local playerSource = GetPlayerSourceByIdentifier(employee.employee_id)
                        if playerSource then
                            -- 员工在线，直接发放
                            local totalPay = employee.salary + employeeBonus
                            AddPlayerMoney(playerSource, totalPay)
                            TriggerClientEvent('lottery:notification', playerSource, '薪资到账', '您收到了¥' .. totalPay .. '的薪资和奖金', 'success')
                            onlineCount = onlineCount + 1
                        else
                            -- 员工离线，记录到离线薪资表
                            MySQL.Async.execute('INSERT INTO lottery_offline_salary (employee_id, employee_name, salary, bonus) VALUES (?, ?, ?, ?)',
                                {employee.employee_id, employee.employee_name, employee.salary, employeeBonus}
                            )
                            offlineCount = offlineCount + 1
                            DebugPrint("^3[彩票系统] ^7员工 " .. employee.employee_name .. " 离线，薪资记录至数据库")
                        end
                    end
                    
                    -- 获取最新账户数据
                    GetShopAccount(function(updatedAccount)
                        -- 获取最新交易记录
                        GetTransactions(100, function(transactions)
                            -- 通知客户端操作成功
                            local message = '已成功发放员工薪资，总金额: ' .. totalSalary
                            if totalBonus > 0 then
                                message = message .. '，奖金: ' .. totalBonus .. '(系统发放)'
                            end
                            
                            message = message .. '\n在线员工: ' .. onlineCount .. '人，离线员工: ' .. offlineCount .. '人'
                            if offlineCount > 0 then
                                message = message .. '\n离线员工薪资已保存，他们下次上线时将自动领取'
                            end
                            
                            TriggerClientEvent('lottery:notification', adminPlayer.source, '操作成功', message, 'success')
                            
                            -- 更新账户余额
                            TriggerClientEvent('lottery:updateAccountBalance', adminPlayer.source, deductAmount, false, updatedAccount, true, GetPlayerMoney(adminPlayer.source), transactions)
                            
                            -- 使用通用函数刷新员工列表
                            Citizen.SetTimeout(500, function() -- 稍微延迟以确保数据库更新完成
                                RefreshEmployeeList(adminPlayer)
                            end)
                        end)
                    end)
                end
            )
        end)
    end)
    
    -- 清除员工和账户缓存
    ClearCache("employees")
    ClearCache("shopAccount")
    ClearCache("transactions")
end

-- 获取员工操作日志
function GetEmployeeLogs(employeeId, cb)
    MySQL.Async.fetchAll('SELECT * FROM lottery_employee_logs WHERE employee_id = ? ORDER BY timestamp DESC LIMIT 100', {employeeId}, function(result)
        cb(result or {})
    end)
end

-- 记录员工操作日志
function LogEmployeeAction(employeeId, employeeName, action, details, adminPlayer)
    local adminId = nil
    local adminName = nil
    
    if adminPlayer then
        if Config.Framework == "ESX" then
            adminId = adminPlayer.identifier
            adminName = adminPlayer.getName()
        elseif Config.Framework == "QB" then
            adminId = adminPlayer.PlayerData.citizenid
            adminName = adminPlayer.PlayerData.charinfo.firstname .. ' ' .. adminPlayer.PlayerData.charinfo.lastname
        end
    end
    
    MySQL.Async.execute('INSERT INTO lottery_employee_logs (employee_id, employee_name, action, details, admin_id, admin_name) VALUES (?, ?, ?, ?, ?, ?)',
        {employeeId, employeeName, action, details, adminId, adminName}
    )
end

-- 获取职业等级列表
function GetJobGrades(cb)
    -- 检查是否有缓存
    if GetCachedData("jobGrades", false, cb) then
        DebugPrint("^2[彩票系统] ^7使用缓存的职业等级列表")
        return
    end
    
    DebugPrint("^3[彩票系统] ^7正在获取职业等级列表...")
    
    local jobName = Config.LotteryJob.name
    local grades = {}
    
    DebugPrint("^3[彩票系统] ^7职业名称: " .. jobName)
    
    if Config.Framework == "ESX" then
        -- ESX框架从job_grades表获取职业等级
        MySQL.Async.fetchAll('SELECT grade, label, name FROM job_grades WHERE job_name = ? ORDER BY grade ASC', {jobName}, function(result)
            if result and #result > 0 then
                DebugPrint("^2[彩票系统] ^7从数据库获取到" .. #result .. "个职业等级")
                for i, grade in ipairs(result) do
                    DebugPrint("^2[彩票系统] ^7职业等级 " .. i .. ": 等级=" .. grade.grade .. ", 名称=" .. grade.name .. ", 标签=" .. grade.label)
                end
                grades = result
            else
                -- 如果数据库中没有找到，使用配置文件中的等级
                DebugPrint("^3[彩票系统] ^7数据库中未找到职业等级，使用配置文件中的等级")
                for i, grade in ipairs(Config.LotteryJob.grades) do
                    DebugPrint("^3[彩票系统] ^7配置文件职业等级 " .. i .. ": 等级=" .. grade.grade .. ", 名称=" .. grade.name .. ", 标签=" .. grade.label)
                    table.insert(grades, {
                        grade = grade.grade,
                        name = grade.name,
                        label = grade.label
                    })
                end
            end
            
            -- 检查最终返回的职业等级数据
            DebugPrint("^2[彩票系统] ^7最终返回 " .. #grades .. " 个职业等级")
            for i, grade in ipairs(grades) do
                DebugPrint("^2[彩票系统] ^7返回职业等级 " .. i .. ": 等级=" .. grade.grade .. ", 名称=" .. (grade.name or "nil") .. ", 标签=" .. (grade.label or "nil"))
            end
            
            -- 缓存结果
            SetCacheData("jobGrades", grades)
            cb(grades)
        end)
    elseif Config.Framework == "QB" then
        -- QB框架使用配置文件中的等级
        for i, grade in ipairs(Config.LotteryJob.grades) do
            DebugPrint("^3[彩票系统] ^7配置文件职业等级 " .. i .. ": 等级=" .. grade.grade .. ", 名称=" .. grade.name .. ", 标签=" .. grade.label)
            table.insert(grades, {
                grade = grade.grade,
                name = grade.name,
                label = grade.label
            })
        end
        DebugPrint("^2[彩票系统] ^7使用配置文件中的" .. #grades .. "个职业等级")
        
        -- 检查最终返回的职业等级数据
        DebugPrint("^2[彩票系统] ^7最终返回 " .. #grades .. " 个职业等级")
        for i, grade in ipairs(grades) do
            DebugPrint("^2[彩票系统] ^7返回职业等级 " .. i .. ": 等级=" .. grade.grade .. ", 名称=" .. (grade.name or "nil") .. ", 标签=" .. (grade.label or "nil"))
        end
        
        -- 缓存结果
        SetCacheData("jobGrades", grades)
        cb(grades)
    else
        -- 未知框架，使用配置文件中的等级
        for i, grade in ipairs(Config.LotteryJob.grades) do
            DebugPrint("^3[彩票系统] ^7配置文件职业等级 " .. i .. ": 等级=" .. grade.grade .. ", 名称=" .. grade.name .. ", 标签=" .. grade.label)
            table.insert(grades, {
                grade = grade.grade,
                name = grade.name,
                label = grade.label
            })
        end
        DebugPrint("^3[彩票系统] ^7未知框架，使用配置文件中的" .. #grades .. "个职业等级")
        
        -- 检查最终返回的职业等级数据
        DebugPrint("^2[彩票系统] ^7最终返回 " .. #grades .. " 个职业等级")
        for i, grade in ipairs(grades) do
            DebugPrint("^2[彩票系统] ^7返回职业等级 " .. i .. ": 等级=" .. grade.grade .. ", 名称=" .. (grade.name or "nil") .. ", 标签=" .. (grade.label or "nil"))
        end
        
        -- 缓存结果
        SetCacheData("jobGrades", grades)
        cb(grades)
    end
end

-- 导出函数
exports('GetLotteryShopAccount', GetShopAccount)
exports('LogTransaction', LogTransaction)
exports('LogLotteryTransaction', function(type, amount)
    MySQL.Async.execute('UPDATE lottery_shop_accounts SET balance = balance + ?, total_' .. (amount > 0 and 'income' or 'payout') .. ' = total_' .. (amount > 0 and 'income' or 'payout') .. ' + ? WHERE id = 1', 
        {amount, math.abs(amount)}
    )
end)

-- 添加获取账户信息的服务器事件
RegisterNetEvent('lottery:getShopAccount')
AddEventHandler('lottery:getShopAccount', function()
    local src = source
    -- 获取玩家
    local player = GetPlayerFromId(src)
    
    -- 如果玩家不存在或不是彩票店职业，则返回
    if not player or not IsPlayerAdmin(player) then
        TriggerClientEvent('lottery:notification', src, '错误', '您没有权限查看账户信息，只有彩票店职业的人才能访问', 'error')
        return
    end
    
    DebugPrint("^2[彩票系统] ^7玩家请求刷新账户数据，ID: " .. src)
    
    -- 获取最新账户数据并发送给客户端
    -- 强制刷新缓存
    ClearCache("shopAccount")
    ClearCache("transactions")
    
    GetShopAccount(function(account)
        -- 确保账户数据有效
        if account then
            -- 获取最新的交易记录
            GetTransactions(100, function(transactions)
                -- 更新客户端数据，传递true作为success参数，并包含交易明细
                TriggerClientEvent('lottery:updateAccountBalance', src, 0, true, account, true, GetPlayerMoney(src), transactions)
            
                -- 打印日志
                DebugPrint("^2[彩票系统] ^7向玩家发送账户信息: " .. src, 
                    "余额=" .. account.balance, 
                    "总收入=" .. account.total_income, 
                    "总支出=" .. account.total_payout,
                    "交易记录数=" .. #transactions)
            end)
        else
            DebugPrint("^1[彩票系统] ^7错误: 无法获取账户数据")
            TriggerClientEvent('lottery:notification', src, '错误', '无法获取账户数据', 'error')
        end
    end)
end)

-- 获取员工在线状态
function GetEmployeeOnlineStatus(employees)
    -- 初始化在线状态表
    local onlineStatus = {}
    
    -- 遍历所有员工
    for _, employee in pairs(employees) do
        -- 默认设置为离线
        onlineStatus[employee.employee_id] = false
    end
    
    -- 获取所有在线玩家ID
    local onlinePlayers = {}
    
    -- 根据框架类型获取在线玩家列表
    if Config.Framework == "ESX" then
        -- ESX框架
        if Framework.GetExtendedPlayers then
            -- ESX Legacy方法
            local players = Framework.GetExtendedPlayers()
            for _, xPlayer in pairs(players) do
                local id = xPlayer.identifier or xPlayer.getIdentifier()
                if id then
                    onlinePlayers[id] = true
                    DebugPrint("^2[彩票系统] ^7ESX在线玩家: " .. id)
                end
            end
        elseif Framework.GetPlayers then
            -- 老版本ESX方法
            local playerIds = Framework.GetPlayers()
            for _, playerId in ipairs(playerIds) do
                local xPlayer = Framework.GetPlayerFromId(playerId)
                if xPlayer then
                    local id = xPlayer.identifier or xPlayer.getIdentifier()
                    if id then
                        onlinePlayers[id] = true
                        DebugPrint("^2[彩票系统] ^7ESX在线玩家(老版本): " .. id)
                    end
                end
            end
        end
    elseif Config.Framework == "QB" then
        -- QB框架
        if Framework.Functions then
            if type(Framework.Functions.GetQBPlayers) == "function" then
                -- QB-Core方法
                local players = Framework.Functions.GetQBPlayers()
                for _, player in pairs(players) do
                    if player.PlayerData and player.PlayerData.citizenid then
                        onlinePlayers[player.PlayerData.citizenid] = true
                        DebugPrint("^2[彩票系统] ^7QB在线玩家: " .. player.PlayerData.citizenid)
                    end
                end
            elseif type(Framework.Functions.GetPlayers) == "function" then
                -- 另一种QB方法
                local playerIds = Framework.Functions.GetPlayers()
                for _, playerId in ipairs(playerIds) do
                    local player = Framework.Functions.GetPlayer(playerId)
                    if player and player.PlayerData and player.PlayerData.citizenid then
                        onlinePlayers[player.PlayerData.citizenid] = true
                        DebugPrint("^2[彩票系统] ^7QB在线玩家(遍历): " .. player.PlayerData.citizenid)
                    end
                end
            end
        end
    else
        -- 通用方法
        local playerIds = GetPlayers()
        for _, playerId in ipairs(playerIds) do
            local playerIdentifier = GetPlayerIdentifier(playerId, 0) -- 获取主标识符
            if playerIdentifier then
                onlinePlayers[playerIdentifier] = true
                DebugPrint("^2[彩票系统] ^7通用方法在线玩家: " .. playerIdentifier)
            end
        end
    end
    
    -- 检查员工是否在线
    for _, employee in pairs(employees) do
        if onlinePlayers[employee.employee_id] then
            onlineStatus[employee.employee_id] = true
            DebugPrint("^2[彩票系统] ^7员工在线: " .. employee.employee_name .. " (ID: " .. employee.employee_id .. ")")
        end
    end
    
    -- 打印调试信息
    local onlineCount = 0
    for id, isOnline in pairs(onlineStatus) do
        if isOnline then
            onlineCount = onlineCount + 1
        end
    end
    DebugPrint("^3[彩票系统] ^7员工在线状态检查完成，共有 " .. onlineCount .. " 名员工在线")
    
    return onlineStatus
end

-- 获取员工列表
function GetEmployeeList(callback)
    MySQL.Async.fetchAll('SELECT * FROM lottery_employees WHERE status != "fired" ORDER BY hire_date DESC', {}, function(employees)
        if employees and #employees > 0 then
            -- 获取员工的在线状态
            local onlineStatus = GetEmployeeOnlineStatus(employees)
            
            -- 将在线状态添加到员工数据中
            for i, employee in ipairs(employees) do
                employee.onlineStatus = onlineStatus[employee.employee_id] and "online" or "offline"
                DebugPrint("^3[彩票系统] ^7员工 " .. employee.employee_name .. " 状态: " .. employee.onlineStatus)
            end
            
            if callback then
                callback(employees)
            end
        else
            if callback then
                callback({})
            end
        end
    end)
end

-- 导出缓存系统
exports('GetCachedData', GetCachedData)
exports('SetCacheData', SetCacheData)
exports('ClearCache', ClearCache)
exports('ClearAllCache', ClearAllCache)

-- 手续费分成设置相关回调函数已移除

-- 获取员工手续费分成设置
RegisterNetEvent('caipiaoc:getEmployeeCommissionSettings')
AddEventHandler('caipiaoc:getEmployeeCommissionSettings', function(data)
    local source = source
    -- 检查权限
    if not HasPermission(source, Config.Permissions.admin) then
        TriggerClientEvent('lottery:notification', source, "没有权限", "无法获取设置", "error")
        return
    end
    
    -- 验证参数
    if not data or not data.employeeId then
        TriggerClientEvent('lottery:notification', source, "参数错误", "获取设置失败", "error")
        return
    end
    
    -- 查询数据库获取员工手续费分成设置
    MySQL.Async.fetchAll('SELECT fee_commission_enabled, fee_commission_rate, fee_commission_min_amount FROM lottery_employees WHERE employee_id = ?', {data.employeeId}, function(result)
        local settings = {
            enabled = false,
            rate = 0.1,  -- 默认10%
            minFeeAmount = 500  -- 默认最小500
        }
        
        -- 如果有记录，使用数据库中的设置
        if result and #result > 0 then
            -- 检查数据类型和值
            local enabledValue = result[1].fee_commission_enabled
            SystemPrint("^2[彩票系统] ^7获取设置: 数据库中的enabled原始值=" .. tostring(enabledValue) .. ", 类型=" .. type(enabledValue))
            
            -- 确保正确解析布尔值 (MySQL值可能是数字、字符串或布尔值)
            settings.enabled = (enabledValue == 1 or enabledValue == true or enabledValue == "1" or enabledValue == "true")
            settings.rate = result[1].fee_commission_rate or 0.1
            settings.minFeeAmount = result[1].fee_commission_min_amount or 500
            
            SystemPrint("^2[彩票系统] ^7返回手续费分成设置: enabled=" .. tostring(settings.enabled) .. 
                       ", rate=" .. tostring(settings.rate) .. 
                       ", minFeeAmount=" .. tostring(settings.minFeeAmount))
        end
        
        -- 返回设置
        TriggerClientEvent('lottery:updateEmployeeCommissionSettings', source, { 
            success = true, 
            settings = settings
        })
    end)
end)

-- 保存员工手续费分成设置
RegisterNetEvent('caipiaoc:saveEmployeeCommissionSettings')
AddEventHandler('caipiaoc:saveEmployeeCommissionSettings', function(data)
    local source = source
    -- 检查权限
    if not HasPermission(source, Config.Permissions.admin) then
        SystemPrint("^1[彩票系统] ^7权限不足: 玩家ID=" .. tostring(source) .. " 尝试保存手续费分成设置")
        TriggerClientEvent('lottery:notification', source, "没有权限", "保存失败", "error")
        return
    end
    
    -- 验证数据
    if not data.employeeId or data.rate == nil or data.minFeeAmount == nil then
        SystemPrint("^1[彩票系统] ^7参数错误: employeeId=" .. tostring(data.employeeId) .. ", rate=" .. tostring(data.rate) .. ", minFeeAmount=" .. tostring(data.minFeeAmount))
        TriggerClientEvent('lottery:notification', source, "参数错误", "保存失败", "error")
        return
    end
    
    -- 转换布尔值为数字
    local enabledValue = data.enabled and 1 or 0
    
    SystemPrint("^3[彩票系统] ^7正在保存手续费分成设置: employeeId=" .. tostring(data.employeeId) .. 
                ", enabled=" .. tostring(data.enabled) .. "(" .. tostring(enabledValue) .. ")" .. 
                ", rate=" .. tostring(data.rate) .. 
                ", minFeeAmount=" .. tostring(data.minFeeAmount))
    
    -- 保存到数据库
    MySQL.Async.execute(
        "UPDATE lottery_employees SET fee_commission_enabled = ?, fee_commission_rate = ?, fee_commission_min_amount = ? WHERE employee_id = ?",
        {enabledValue, data.rate, data.minFeeAmount, data.employeeId},
        function(rowsChanged)
            SystemPrint(string.format("^3[彩票系统] ^7员工(%s)手续费分成设置已更新: enabled=%s, rate=%.2f, minAmount=%d, 影响行数=%d", 
                tostring(data.employeeId), tostring(data.enabled), data.rate, data.minFeeAmount, rowsChanged))
            
            if rowsChanged > 0 then
                SystemPrint("^2[彩票系统] ^7数据库更新成功: 影响了 " .. rowsChanged .. " 行")
            else
                SystemPrint("^1[彩票系统] ^7数据库未更新: 可能没有找到匹配的员工ID或值未发生变化")
            end
            
            -- 通知客户端更新成功
            TriggerClientEvent('lottery:updateEmployeeCommissionSettings', source, { 
                success = true, 
                settings = {
                    enabled = data.enabled,
                    rate = data.rate,
                    minFeeAmount = data.minFeeAmount
                }
            })
        end
    )
end)

-- 根据玩家标识符获取玩家源
function GetPlayerSourceByIdentifier(identifier)
    if not identifier then return nil end

    -- 根据框架类型获取在线玩家列表
    if Config.Framework == "ESX" then
        -- ESX框架
        if Framework.GetExtendedPlayers then
            -- ESX Legacy方法
            local players = Framework.GetExtendedPlayers()
            for _, xPlayer in pairs(players) do
                local id = xPlayer.identifier or xPlayer.getIdentifier()
                if id == identifier then
                    return xPlayer.source
                end
            end
        elseif Framework.GetPlayers then
            -- 老版本ESX方法
            local playerIds = Framework.GetPlayers()
            for _, playerId in ipairs(playerIds) do
                local xPlayer = Framework.GetPlayerFromId(playerId)
                if xPlayer then
                    local id = xPlayer.identifier or xPlayer.getIdentifier()
                    if id == identifier then
                        return playerId
                    end
                end
            end
        end
    elseif Config.Framework == "QB" then
        -- QB框架 - 使用全局QBCore对象
        if QBCore and QBCore.Functions then
            -- 使用原生FiveM方法遍历所有玩家
            local playerIds = GetPlayers()

            for _, playerId in ipairs(playerIds) do
                local player = QBCore.Functions.GetPlayer(tonumber(playerId))
                if player and player.PlayerData and player.PlayerData.citizenid then
                    if player.PlayerData.citizenid == identifier then
                        return tonumber(playerId)
                    end
                end
            end
        end
    else
        -- 通用方法
        local playerIds = GetPlayers()
        for _, playerId in ipairs(playerIds) do
            local playerIdentifier = GetPlayerIdentifier(playerId, 0) -- 获取主标识符
            if playerIdentifier == identifier then
                return tonumber(playerId)
            end
        end
    end

    return nil
end

-- 发放员工奖金
RegisterNetEvent('caipiaoc:payEmployeeBonus')
AddEventHandler('caipiaoc:payEmployeeBonus', function(data)
    local source = source
    local employeeId = data.employeeId
    
    if not employeeId then
        SystemPrint("^1[彩票系统] ^7发放奖金失败: 参数错误")
        TriggerClientEvent('lottery:notification', source, "发放奖金失败", "参数错误", "error")
        return
    end
    
    -- 查询员工奖金
    MySQL.Async.fetchAll('SELECT bonus, employee_name FROM lottery_employees WHERE employee_id = ?', {employeeId}, function(result)
        if not result or #result == 0 then
            SystemPrint("^1[彩票系统] ^7发放奖金失败: 未找到员工记录")
            TriggerClientEvent('lottery:notification', source, "发放奖金失败", "未找到员工记录", "error")
            return
        end
        
        local bonus = tonumber(result[1].bonus) or 0
        local employeeName = result[1].employee_name
        
        if bonus <= 0 then
            SystemPrint("^1[彩票系统] ^7发放奖金失败: 员工没有可发放的奖金")
            TriggerClientEvent('lottery:notification', source, "发放奖金失败", "该员工没有可发放的奖金", "error")
            return
        end
        
        -- 获取在线玩家列表
        local xPlayer = nil
        local onlinePlayerId = nil
        
        for _, playerId in ipairs(GetPlayers()) do
            local identifier = GetPlayerIdentifier(playerId)
            if identifier == employeeId then
                xPlayer = ESX.GetPlayerFromId(playerId)
                onlinePlayerId = playerId
                break
            end
        end
        
        -- 更新数据库
        MySQL.Async.execute('UPDATE lottery_employees SET bonus = 0 WHERE employee_id = ?', {employeeId}, function(rowsChanged)
            if rowsChanged > 0 then
                -- 记录员工奖金发放交易
                exports[GetCurrentResourceName()]:LogTransaction('bonus_payment', bonus, '员工奖金发放', employeeId, employeeName)
                
                -- 如果员工在线，直接发放到账户
                if xPlayer and onlinePlayerId then
                    xPlayer.addMoney(bonus)
                    TriggerClientEvent('lottery:notification', onlinePlayerId, "奖金到账", "您收到了¥" .. bonus .. "的奖金", "success")
                    SystemPrint("^2[彩票系统] ^7已向在线员工发放奖金: " .. employeeName .. ", 金额: ¥" .. bonus)
                else
                    -- 离线员工，记录到日志中
                    SystemPrint("^3[彩票系统] ^7员工不在线，已记录奖金发放: " .. employeeName .. ", 金额: ¥" .. bonus)
                end
                
                -- 向发起请求的管理员发送成功通知
                TriggerClientEvent('lottery:notification', source, "发放奖金成功", "已向 " .. employeeName .. " 发放¥" .. bonus .. "的奖金", "success")
                
                -- 通知所有客户端刷新管理界面
                TriggerClientEvent('lottery:refreshAdminData', -1)
            else
                -- 发放失败
                SystemPrint("^1[彩票系统] ^7发放奖金失败: 数据库更新错误")
                TriggerClientEvent('lottery:notification', source, "发放奖金失败", "数据库更新错误", "error")
            end
        end)
    end)
end)

-- 在文件开头的初始化部分添加创建离线薪资表的代码
-- 在合适的位置，如初始化函数中添加以下代码
Citizen.CreateThread(function()
    -- 创建离线薪资表
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS lottery_offline_salary (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_id VARCHAR(50) NOT NULL,
            employee_name VARCHAR(50) NOT NULL,
            salary INT NOT NULL,
            bonus INT NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            claimed TINYINT(1) DEFAULT 0
        )
    ]], {}, function(result)
        print("^2[彩票系统] ^7离线薪资表检查/创建完成")
    end)
end)

-- 添加玩家上线事件处理，检查离线薪资
AddEventHandler('playerConnecting', function(playerName, setKickReason, deferrals)
    local source = source
    local identifier = GetPlayerIdentifier(source)
    
    if not identifier then return end
    
    -- 延迟处理，确保玩家完全加载
    Citizen.SetTimeout(5000, function()
        CheckOfflineSalary(source, identifier)
    end)
end)

-- 处理玩家上线时的薪资检查
function CheckOfflineSalary(source, identifier)
    if not identifier then 
        identifier = GetPlayerIdentifier(source)
        if not identifier then return end
    end
    
    -- 查询该玩家是否有未领取的薪资
    MySQL.Async.fetchAll('SELECT * FROM lottery_offline_salary WHERE employee_id = ? AND claimed = 0', {identifier}, function(result)
        if result and #result > 0 then
            local totalSalary = 0
            local totalBonus = 0
            
            -- 计算总金额
            for _, salary in ipairs(result) do
                totalSalary = totalSalary + salary.salary
                totalBonus = totalBonus + salary.bonus
            end
            
            local totalAmount = totalSalary + totalBonus
            
            -- 将所有记录标记为已领取
            MySQL.Async.execute('UPDATE lottery_offline_salary SET claimed = 1 WHERE employee_id = ? AND claimed = 0', {identifier}, function()
                -- 给玩家发放薪资
                AddPlayerMoney(source, totalAmount)
                
                -- 通知玩家
                TriggerClientEvent('lottery:notification', source, '离线薪资', '您收到了离线时的薪资和奖金共计: ¥' .. totalAmount, 'success')
                DebugPrint("^2[彩票系统] ^7玩家 " .. GetPlayerName(source) .. " 收到离线薪资: ¥" .. totalAmount)
            end)
        end
    end)
end

-- 修改发放员工薪资函数，处理离线员工
function PayEmployeeSalary(adminPlayer)
    -- 获取所有活跃员工
    MySQL.Async.fetchAll('SELECT * FROM lottery_employees WHERE status = ?', {'active'}, function(employees)
        if not employees or #employees == 0 then
            TriggerClientEvent('lottery:notification', adminPlayer.source, '提示', '没有需要发放薪资的员工', 'info')
            return
        end
        
        -- 计算总薪资和奖金
        local totalSalary = 0
        local totalBonus = 0
        
        for _, employee in ipairs(employees) do
            totalSalary = totalSalary + employee.salary
            totalBonus = totalBonus + (employee.bonus or 0)
        end
        
        -- 修改：账户只扣除薪资，不扣除奖金
        local deductAmount = totalSalary
        
        -- 获取彩票店账户余额 (使用ESX账户系统)
        TriggerEvent('esx_addonaccount:getSharedAccount', 'society_lottery', function(societyAccount)
            if not societyAccount or societyAccount.money < deductAmount then
                TriggerClientEvent('lottery:notification', adminPlayer.source, '错误', '账户余额不足，无法发放薪资', 'error')
                return
            end
            
            -- 从社团账户扣除薪资总额
            societyAccount.removeMoney(deductAmount)
            
            -- 更新所有员工的最后发薪时间并清零奖金
            MySQL.Async.execute('UPDATE lottery_employees SET last_payment = CURRENT_TIMESTAMP, bonus = 0 WHERE status = ?',
                {'active'},
                function()
                    -- 记录交易明细 - 只记录薪资支出
                    LogTransaction('salary', -totalSalary, '员工薪资发放', GetPlayerIdentifier(adminPlayer.source), GetPlayerName(adminPlayer.source))
                    
                    -- 奖金记录为系统发放，不从账户扣除
                    if totalBonus > 0 then
                        LogTransaction('bonus', 0, '员工奖金发放(系统)', GetPlayerIdentifier(adminPlayer.source), GetPlayerName(adminPlayer.source))
                    end
                    
                    -- 记录每个员工的薪资和奖金发放
                    local onlineCount = 0
                    local offlineCount = 0
                    
                    for _, employee in ipairs(employees) do
                        local employeeBonus = employee.bonus or 0
                        local logDetails = '发放薪资: ' .. employee.salary
                        
                        if employeeBonus > 0 then
                            logDetails = logDetails .. ', 奖金: ' .. employeeBonus .. '(系统发放)'
                        end
                        
                        LogEmployeeAction(employee.employee_id, employee.employee_name, 'salary_bonus', logDetails, adminPlayer)
                        
                        -- 给员工发放薪资和奖金
                        local playerSource = GetPlayerSourceByIdentifier(employee.employee_id)
                        if playerSource then
                            -- 员工在线，直接发放
                            local totalPay = employee.salary + employeeBonus
                            AddPlayerMoney(playerSource, totalPay)
                            TriggerClientEvent('lottery:notification', playerSource, '薪资到账', '您收到了¥' .. totalPay .. '的薪资和奖金', 'success')
                            onlineCount = onlineCount + 1
                        else
                            -- 员工离线，记录到离线薪资表
                            MySQL.Async.execute('INSERT INTO lottery_offline_salary (employee_id, employee_name, salary, bonus) VALUES (?, ?, ?, ?)',
                                {employee.employee_id, employee.employee_name, employee.salary, employeeBonus}
                            )
                            offlineCount = offlineCount + 1
                            DebugPrint("^3[彩票系统] ^7员工 " .. employee.employee_name .. " 离线，薪资记录至数据库")
                        end
                    end
                    
                    -- 获取最新账户数据
                    GetShopAccount(function(updatedAccount)
                        -- 获取最新交易记录
                        GetTransactions(100, function(transactions)
                            -- 通知客户端操作成功
                            local message = '已成功发放员工薪资，总金额: ' .. totalSalary
                            if totalBonus > 0 then
                                message = message .. '，奖金: ' .. totalBonus .. '(系统发放)'
                            end
                            
                            message = message .. '\n在线员工: ' .. onlineCount .. '人，离线员工: ' .. offlineCount .. '人'
                            if offlineCount > 0 then
                                message = message .. '\n离线员工薪资已保存，他们下次上线时将自动领取'
                            end
                            
                            TriggerClientEvent('lottery:notification', adminPlayer.source, '操作成功', message, 'success')
                            
                            -- 更新账户余额
                            TriggerClientEvent('lottery:updateAccountBalance', adminPlayer.source, deductAmount, false, updatedAccount, true, GetPlayerMoney(adminPlayer.source), transactions)
                            
                            -- 使用通用函数刷新员工列表
                            Citizen.SetTimeout(500, function() -- 稍微延迟以确保数据库更新完成
                                RefreshEmployeeList(adminPlayer)
                            end)
                        end)
                    end)
                end
            )
        end)
    end)
    
    -- 清除员工和账户缓存
    ClearCache("employees")
    ClearCache("shopAccount")
    ClearCache("transactions")
end

-- 彩票配置相关函数 - 直接从config.lua获取配置
function GetLotteryConfigData()
    -- 直接从config.lua配置文件获取数据，不使用JSON文件
    local configData = {
        double_ball = {
            price = Config.DoubleBall.price,
            probability = 0.0, -- 在开发版本中计算或填入概率，生产版本使用实际奖池分析计算
            prizes = {},
            drawTime = Config.DoubleBall.drawTime,
            drawDays = Config.DoubleBall.drawDays
        },
        super_lotto = {
            price = Config.SuperLotto.price,
            probability = 0.0, -- 在开发版本中计算或填入概率，生产版本使用实际奖池分析计算
            prizes = {},
            drawTime = Config.SuperLotto.drawTime,
            drawDays = Config.SuperLotto.drawDays
        },
        scratch_cards = {}
    }

    -- 添加调试信息，显示直接从config.lua获取的配置
    DebugPrint("GetLotteryConfigData调用 - 双色球价格: " .. Config.DoubleBall.price .. ", 一等奖: " .. Config.DoubleBall.prizes[1].amount .. ", 大乐透价格: " .. Config.SuperLotto.price)
    
    -- 计算双色球基本中奖概率（六等奖概率）
    -- 双色球有10种不同的中奖情况，这里简化为计算六等奖的概率作为"基本中奖率"
    local redBallSpace = binomialCoefficient(Config.DoubleBall.redBalls, Config.DoubleBall.selectRed)
    local blueBallSpace = Config.DoubleBall.blueBalls
    local totalSpace = redBallSpace * blueBallSpace
    
    -- 计算六等奖概率 (0红+1蓝, 1红+1蓝, 2红+1蓝)
    local sixthPrizeWays = binomialCoefficient(Config.DoubleBall.redBalls - Config.DoubleBall.selectRed, Config.DoubleBall.selectRed) * 1
    local sixthPrizeProb = sixthPrizeWays / totalSpace
    configData.double_ball.probability = string.format("%.3f", sixthPrizeProb)
    
    -- 计算大乐透基本中奖概率（八等奖概率）
    local frontSpace = binomialCoefficient(Config.SuperLotto.frontBalls, Config.SuperLotto.selectFront)
    local backSpace = binomialCoefficient(Config.SuperLotto.backBalls, Config.SuperLotto.selectBack)
    local superTotalSpace = frontSpace * backSpace
    
    -- 计算八等奖概率 (0前+2后)
    local eighthPrizeWays = binomialCoefficient(Config.SuperLotto.frontBalls - Config.SuperLotto.selectFront, Config.SuperLotto.selectFront) * 1
    local eighthPrizeProb = eighthPrizeWays / superTotalSpace
    configData.super_lotto.probability = string.format("%.3f", eighthPrizeProb)
    
    -- 添加奖项配置
    for id, prize in pairs(Config.DoubleBall.prizes) do
        configData.double_ball.prizes[tostring(id)] = {
            name = prize.name,
            match = prize.match,
            amount = prize.amount,
            poolPercent = prize.poolPercent
        }
    end
    
    for id, prize in pairs(Config.SuperLotto.prizes) do
        configData.super_lotto.prizes[tostring(id)] = {
            name = prize.name,
            match = prize.match,
            amount = prize.amount,
            poolPercent = prize.poolPercent
        }
    end
    
    -- 填充刮刮乐配置
    for cardType, cardConfig in pairs(Config.ScratchCards) do
        configData.scratch_cards[cardType] = {
            name = cardConfig.name,
            price = cardConfig.price,
            maxPrize = cardConfig.maxPrize
        }
        
        -- 添加金额权重配置
        if cardType == 'scratch_xixiangfeng' and cardConfig.amountRates then
            configData.scratch_cards[cardType].amountRates = cardConfig.amountRates
        elseif cardType == 'scratch_fusong' and cardConfig.rowAmountRates then
            configData.scratch_cards[cardType].rowAmountRates = cardConfig.rowAmountRates
        elseif cardType == 'scratch_yaocai' and cardConfig.winningAmountRates then
            configData.scratch_cards[cardType].winningAmountRates = cardConfig.winningAmountRates
        elseif cardType == 'scratch_caizuan' and cardConfig.winningItemRates then
            configData.scratch_cards[cardType].winningItemRates = cardConfig.winningItemRates
        elseif cardType == 'scratch_zhongguofu' and cardConfig.winningItemRates then
            configData.scratch_cards[cardType].winningItemRates = cardConfig.winningItemRates
        elseif cardType == 'scratch_chengfeng' then
            if cardConfig.iconItemRates then
                configData.scratch_cards[cardType].iconItemRates = cardConfig.iconItemRates
            end
            if cardConfig.winningItemRates then
                configData.scratch_cards[cardType].winningItemRates = cardConfig.winningItemRates
            end
        end
    end
    
    -- 填充刮刮乐符号配置
    configData.scratch_symbols = {}

    -- 添加喜相逢符号配置
    if Config.ScratchSymbols then
        if Config.ScratchSymbols.xiRate then
            configData.scratch_symbols.xiRate = Config.ScratchSymbols.xiRate
        end
        if Config.ScratchSymbols.xiXiRate then
            configData.scratch_symbols.xiXiRate = Config.ScratchSymbols.xiXiRate
        end
        if Config.ScratchSymbols.allSpecialRate then
            configData.scratch_symbols.allSpecialRate = Config.ScratchSymbols.allSpecialRate
        end
    end

    -- 添加福鼠送彩匹配数量权重配置
    if Config.ScratchSymbols and Config.ScratchSymbols.fusongMatchRates then
        configData.scratch_symbols.fusongMatchRates = Config.ScratchSymbols.fusongMatchRates
    end

    -- 添加耀出彩匹配数量权重配置
    if Config.ScratchSymbols and Config.ScratchSymbols.yaocaiMatchRates then
        configData.scratch_symbols.yaocaiMatchRates = Config.ScratchSymbols.yaocaiMatchRates
    end

    -- 添加5倍彩钻匹配数量权重配置
    if Config.ScratchSymbols and Config.ScratchSymbols.caizuanMatchRates then
        configData.scratch_symbols.caizuanMatchRates = Config.ScratchSymbols.caizuanMatchRates
    end

    -- 添加5倍彩钻钻石图案数量权重配置
    if Config.ScratchSymbols and Config.ScratchSymbols.caizuanDiamondRates then
        configData.scratch_symbols.caizuanDiamondRates = Config.ScratchSymbols.caizuanDiamondRates
    end

    -- 添加中国福匹配数量权重配置
    if Config.ScratchSymbols and Config.ScratchSymbols.zhongguofuMatchRates then
        configData.scratch_symbols.zhongguofuMatchRates = Config.ScratchSymbols.zhongguofuMatchRates
    end

    -- 添加中国福福符号数量权重配置
    if Config.ScratchSymbols and Config.ScratchSymbols.zhongguofuFuRates then
        configData.scratch_symbols.zhongguofuFuRates = Config.ScratchSymbols.zhongguofuFuRates
    end

    -- 添加乘风破浪⛵符号数量权重配置
    if Config.ScratchSymbols and Config.ScratchSymbols.chengfengSailRates then
        configData.scratch_symbols.chengfengSailRates = Config.ScratchSymbols.chengfengSailRates
    end

    -- 添加乘风破浪🌪符号数量权重配置
    if Config.ScratchSymbols and Config.ScratchSymbols.chengfengTornadoRates then
        configData.scratch_symbols.chengfengTornadoRates = Config.ScratchSymbols.chengfengTornadoRates
    end

    -- 添加物品名称映射
    if Config.Items and Config.Items.itemNames then
        configData.itemNames = Config.Items.itemNames
    end

    return configData
end

-- 辅助函数：计算组合数 C(n,k)
function binomialCoefficient(n, k)
    if k < 0 or k > n then
        return 0
    end
    if k == 0 or k == n then
        return 1
    end
    
    local result = 1
    for i = 1, k do
        result = result * (n - (i - 1)) / i
    end
    
    return result
end

-- 保存彩票配置
function SaveLotteryConfig(source, data)
    local src = source
    
    -- 检查是否启用可配置选项
    if not Config.LotteryConfig.configurable then
        TriggerClientEvent('lottery:notification', src, '彩票配置功能已禁用', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '彩票配置功能已禁用'
        })
        return
    end
    
    -- 检查权限
    local hasPermission = HasAdminPermission(src)
    if not hasPermission then
        TriggerClientEvent('lottery:notification', src, '您没有权限修改彩票配置', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '权限不足'
        })
        return
    end
    
    -- 验证参数
    if not data.type or not data.price or not data.probability then
        TriggerClientEvent('lottery:notification', src, '无效的配置参数', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '无效的配置参数'
        })
        return
    end
    
    -- 价格范围验证
    if data.price < Config.LotteryConfig.minPrice or data.price > Config.LotteryConfig.maxPrice then
        TriggerClientEvent('lottery:notification', src, '价格必须在'..Config.LotteryConfig.minPrice..'到'..Config.LotteryConfig.maxPrice..'之间', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '价格范围无效'
        })
        return
    end
    
    -- 概率范围验证
    if data.probability < Config.LotteryConfig.minProbability or data.probability > Config.LotteryConfig.maxProbability then
        TriggerClientEvent('lottery:notification', src, '概率必须在'..Config.LotteryConfig.minProbability..'到'..Config.LotteryConfig.maxProbability..'之间', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '概率范围无效'
        })
        return
    end
    
    -- 更新配置
    if data.type == 'double_ball' then
        Config.DoubleBall.price = data.price
        -- 概率计算较为复杂，这里仅保存记录
        -- 在实际服务器重启后会重置为默认值
    elseif data.type == 'super_lotto' then
        Config.SuperLotto.price = data.price
        -- 同上
    else
        TriggerClientEvent('lottery:notification', src, '无效的彩票类型', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '无效的彩票类型'
        })
        return
    end
    
    -- 记录管理员操作
    local adminName = GetPlayerName(src)
    LogAdminAction(src, adminName, 'update_lottery_config', '更新彩票配置: ' .. data.type, 0)
    
    -- 返回成功消息和更新后的配置
    TriggerClientEvent('lottery:notification', src, '彩票配置已更新', 'success')
    TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
        success = true,
        config = GetLotteryConfigData()
    })
    
    -- 广播配置更新
    TriggerClientEvent('lottery:refreshLotteryConfig', -1, GetLotteryConfigData())

    -- 永久保存配置到config.lua文件
    SaveConfigToLuaFile()
    DebugPrint("配置已永久保存并更新")
end

-- 保存所有彩票配置
function SaveAllLotteryConfig(source, data)
    local src = source
    
    -- 检查是否启用可配置选项
    if not Config.LotteryConfig.configurable then
        TriggerClientEvent('lottery:notification', src, '彩票配置功能已禁用', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '彩票配置功能已禁用'
        })
        return
    end
    
    -- 检查权限
    local hasPermission = HasAdminPermission(src)
    if not hasPermission then
        TriggerClientEvent('lottery:notification', src, '您没有权限修改彩票配置', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '权限不足'
        })
        return
    end
    
    -- 验证参数
    if not data.double_ball or not data.super_lotto then
        TriggerClientEvent('lottery:notification', src, '无效的配置参数', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '无效的配置参数'
        })
        return
    end
    
    -- 双色球配置验证
    if not data.double_ball.price or not data.double_ball.probability then
        TriggerClientEvent('lottery:notification', src, '双色球配置参数不完整', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '双色球配置参数不完整'
        })
        return
    end
    
    -- 大乐透配置验证
    if not data.super_lotto.price or not data.super_lotto.probability then
        TriggerClientEvent('lottery:notification', src, '大乐透配置参数不完整', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '大乐透配置参数不完整'
        })
        return
    end
    
    -- 双色球价格范围验证
    if data.double_ball.price < Config.LotteryConfig.minPrice or data.double_ball.price > Config.LotteryConfig.maxPrice then
        TriggerClientEvent('lottery:notification', src, '双色球价格必须在'..Config.LotteryConfig.minPrice..'到'..Config.LotteryConfig.maxPrice..'之间', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '双色球价格范围无效'
        })
        return
    end
    
    -- 大乐透价格范围验证
    if data.super_lotto.price < Config.LotteryConfig.minPrice or data.super_lotto.price > Config.LotteryConfig.maxPrice then
        TriggerClientEvent('lottery:notification', src, '大乐透价格必须在'..Config.LotteryConfig.minPrice..'到'..Config.LotteryConfig.maxPrice..'之间', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '大乐透价格范围无效'
        })
        return
    end
    
    -- 更新配置
    Config.DoubleBall.price = data.double_ball.price
    Config.SuperLotto.price = data.super_lotto.price
    
    -- 记录管理员操作
    local adminName = GetPlayerName(src)
    LogAdminAction(src, adminName, 'update_all_lottery_config', '更新所有彩票配置', 0)
    
    -- 返回成功消息和更新后的配置
    TriggerClientEvent('lottery:notification', src, '所有彩票配置已更新', 'success')
    TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
        success = true,
        config = GetLotteryConfigData()
    })
    
    -- 广播配置更新
    TriggerClientEvent('lottery:refreshLotteryConfig', -1, GetLotteryConfigData())

    -- 永久保存配置到config.lua文件
    SaveConfigToLuaFile()
    DebugPrint("所有彩票配置已永久保存")
end

-- 检查玩家是否有管理员权限
function HasAdminPermission(source)
    local player = GetPlayerFromId(source)
    if not player then return false end
    
    if Config.Framework == "ESX" then
        -- ESX框架下使用xPlayer.getGroup()检查是否为admin或superadmin
        local xPlayer = player
        local group = xPlayer.getGroup()
        return group == 'admin' or group == 'superadmin'
    elseif Config.Framework == "QB" then
        -- QB框架下使用多种方式检查管理员权限
        local hasAdmin = false
        local hasGod = false
        local hasMod = false

        -- 方法1: 使用QBCore.Functions.HasPermission (如果存在)
        if Framework.Functions.HasPermission then
            hasAdmin = Framework.Functions.HasPermission(source, 'admin')
            hasGod = Framework.Functions.HasPermission(source, 'god')
            hasMod = Framework.Functions.HasPermission(source, 'mod')
        end

        -- 方法2: 检查玩家数据中的权限组
        if not (hasGod or hasAdmin or hasMod) then
            if player and player.PlayerData then
                -- 检查权限组
                if player.PlayerData.permission then
                    local permission = player.PlayerData.permission
                    hasGod = permission == 'god'
                    hasAdmin = permission == 'admin'
                    hasMod = permission == 'mod'
                end
            end
        end

        -- 方法3: 使用IsPlayerAceAllowed (FiveM原生权限系统)
        if not (hasGod or hasAdmin or hasMod) then
            hasGod = IsPlayerAceAllowed(source, 'command.god') or IsPlayerAceAllowed(source, 'group.god')
            hasAdmin = IsPlayerAceAllowed(source, 'command.admin') or IsPlayerAceAllowed(source, 'group.admin')
            hasMod = IsPlayerAceAllowed(source, 'command.mod') or IsPlayerAceAllowed(source, 'group.mod')
        end

        return hasAdmin or hasGod or hasMod
    end
    
    return false
end

-- 检查玩家是否有职业权限访问管理系统
function HasJobPermission(source)
    local player = GetPlayerFromId(source)
    if not player then return false end
    
    -- 检查职业系统是否启用
    if Config.LotteryJob.enabled ~= true then
        return false
    end
    
    -- 检查玩家是否为彩票店职业
    if Config.Framework == "ESX" then
        local job = player.getJob()
        if not job or job.name ~= Config.LotteryJob.name then
            return false
        end
        
        -- 只要是彩票店职业就可以访问，不再检查职业等级
        return true
    elseif Config.Framework == "QB" then
        local job = player.PlayerData.job
        if not job or job.name ~= Config.LotteryJob.name then
            return false
        end
        
        -- 只要是彩票店职业就可以访问，不再检查职业等级
        return true
    end
    
    return false
end

-- 检查玩家是否有权限访问彩票配置
function IsBossPermission(source)
    local player = GetPlayerFromId(source)
    if not player then return false end
    
    -- 检查职业系统是否启用
    if Config.LotteryJob.enabled ~= true then
        -- 如果职业系统未启用，则回退到管理员权限检查
        return HasAdminPermission(source)
    end
    
    -- 检查玩家是否为彩票店职业
    if Config.Framework == "ESX" then
        local job = player.getJob()
        if not job or job.name ~= Config.LotteryJob.name then
            return false
        end
        
        -- 使用配置中的adminAccess值判断权限
        return job.grade >= Config.LotteryJob.adminAccess
    elseif Config.Framework == "QB" then
        local job = player.PlayerData.job
        if not job or job.name ~= Config.LotteryJob.name then
            return false
        end
        
        -- 使用配置中的adminAccess值判断权限
        return job.grade.level >= Config.LotteryJob.adminAccess
    end
    
    return false
end

-- 获取管理系统数据
function GetAdminSystemData(src)
    local player = GetPlayerFromId(src)
    if not player then return end
    
    -- 获取玩家现金和职业信息
    local playerCash = GetPlayerMoney(src)
    local playerJob = nil
    
    -- 根据框架获取玩家职业信息
    if Config.Framework == "ESX" then
        local job = player.getJob()
        playerJob = {
            name = job.name,
            label = job.label,
            grade = job.grade,
            grade_name = job.grade_name,
            grade_label = job.grade_label
        }
    elseif Config.Framework == "QB" then
        local job = player.PlayerData.job
        playerJob = {
            name = job.name,
            label = job.label,
            grade = job.grade.level,
            grade_name = job.grade.name,
            grade_label = job.grade.name
        }
    end
    
    -- 首先检查是否所有数据都有缓存
    local allCached = true
    local cachedData = {
        playerData = {
            money = playerCash,
            job = playerJob
        },
        config = {
            permissions = {
                adminAccess = Config.LotteryJob.adminAccess or 3 -- 默认需要3级以上才能访问账户管理和员工管理
            }
        }
    }
    
    -- 检查各项数据是否有缓存
    if CacheSystem.data.salesData then
        cachedData.sales = CacheSystem.data.salesData
    else
        allCached = false
    end
    
    if CacheSystem.data.winningRecords then
        cachedData.winningRecords = CacheSystem.data.winningRecords
    else
        allCached = false
    end
    
    if CacheSystem.data.claimRecords then
        cachedData.claimRecords = CacheSystem.data.claimRecords
    else
        allCached = false
    end
    
    if CacheSystem.data.unclaimedRecords then
        cachedData.unclaimedRecords = CacheSystem.data.unclaimedRecords
    else
        allCached = false
    end
    
    if CacheSystem.data.shopAccount then
        cachedData.account = CacheSystem.data.shopAccount
    else
        allCached = false
    end
    
    if CacheSystem.data.transactions then
        cachedData.transactions = CacheSystem.data.transactions
    else
        allCached = false
    end
    
    if CacheSystem.data.employees then
        cachedData.employees = CacheSystem.data.employees
    else
        allCached = false
    end
    
    if CacheSystem.data.jobGrades then
        cachedData.jobGrades = CacheSystem.data.jobGrades
    else
        allCached = false
    end
    
    -- 如果所有数据都有缓存，直接返回缓存数据
    if allCached then
        DebugPrint("^2[彩票系统] ^7使用缓存数据，发送给玩家: " .. src)
        
        -- 检查数据格式，特别是可能包含HTML的字段
        if cachedData.winningRecords then
            -- 检查中奖记录格式
            DebugPrint("^3[彩票系统] ^7发送缓存的中奖记录：刮刮乐=" .. #(cachedData.winningRecords.scratch or {}) .. "，彩票=" .. #(cachedData.winningRecords.lottery or {}))
        end
        
        -- 添加强制重新处理标记，告诉客户端这是缓存数据
        cachedData.fromCache = true
        
        -- 发送缓存数据到客户端
        TriggerClientEvent('lottery:receiveAdminData', src, cachedData)
        return
    end
    
    -- 否则获取销售数据
    GetSalesData(function(salesData)
        -- 获取中奖记录
        GetWinningRecords(function(winningRecords)
            -- 获取兑奖记录
            GetClaimRecords(function(claimRecords)
                -- 获取未兑奖记录
                GetUnclaimedRecords(function(unclaimedRecords)
                    -- 获取账户余额
                    GetShopAccount(function(accountData)
                        -- 获取交易明细
                        GetTransactions(100, function(transactions)
                            -- 获取员工列表
                            GetAllEmployees(function(employees)
                                -- 获取职业等级列表
                                GetJobGrades(function(jobGrades)
                                    -- 创建管理系统数据
                                    local adminData = {
                                        sales = salesData,
                                        winningRecords = winningRecords,
                                        claimRecords = claimRecords,
                                        unclaimedRecords = unclaimedRecords,
                                        account = accountData,
                                        transactions = transactions,
                                        employees = employees,
                                        jobGrades = jobGrades,
                                        playerData = {
                                            money = playerCash,
                                            job = playerJob
                                        },
                                        config = {
                                            permissions = {
                                                adminAccess = Config.LotteryJob.adminAccess or 3 -- 默认需要3级以上才能访问账户管理和员工管理
                                            }
                                        },
                                        fromCache = false -- 标记这是新鲜数据，不是缓存
                                    }
                                    
                                    -- 检查数据格式
                                    if winningRecords then
                                        DebugPrint("^3[彩票系统] ^7发送新鲜的中奖记录：刮刮乐=" .. #(winningRecords.scratch or {}) .. "，彩票=" .. #(winningRecords.lottery or {}))
                                    end
                                    
                                    DebugPrint("^2[彩票系统] ^7管理系统数据准备完成，发送给玩家: " .. src)
                                    DebugPrint("^2[彩票系统] ^7玩家当前现金: " .. playerCash)
                                    
                                    -- 检查销售数据
                                    if salesData and salesData.total then
                                        DebugPrint("^2[彩票系统] ^7销售数据总结:")
                                        DebugPrint("^2[彩票系统] ^7总销量: " .. (salesData.total.total_sold or 0))
                                        DebugPrint("^2[彩票系统] ^7总收入: " .. (salesData.total.total_revenue or 0))
                                        DebugPrint("^2[彩票系统] ^7刮刮乐销量: " .. (salesData.total.total_scratch_sold or 0))
                                        DebugPrint("^2[彩票系统] ^7彩票销量: " .. (salesData.total.total_lottery_sold or 0))
                                    else
                                        DebugPrint("^1[彩票系统] ^7警告: 销售数据无效")
                                    end
                                    
                                    TriggerClientEvent('lottery:receiveAdminData', src, adminData)
                                end)
                            end)
                        end)
                    end)
                end)
            end)
        end)
    end)
end

-- 保存彩票奖项配置
function SavePrizeConfig(source, data)
    local src = source
    
    -- 添加调试输出
    DebugPrint("收到奖项配置请求: " .. json.encode(data))
    
    -- 暂时禁用配置开关检查
    -- if not Config.LotteryConfig.configurable then
    --     TriggerClientEvent('lottery:notification', src, '彩票配置功能已禁用', 'error')
    --     TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
    --         success = false,
    --         message = '彩票配置功能已禁用'
    --     })
    --     return
    -- end
    
    -- 暂时禁用权限检查进行测试
    -- local hasPermission = HasAdminPermission(src)
    -- if not hasPermission then
    --     TriggerClientEvent('lottery:notification', src, '您没有权限修改彩票奖项配置', 'error')
    --     TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
    --         success = false,
    --         message = '权限不足'
    --     })
    --     return
    -- end
    
    -- 验证参数
    if not data.type or not data.prizes then
        TriggerClientEvent('lottery:notification', src, '无效的奖项配置参数', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '无效的奖项配置参数'
        })
        return
    end
    
    -- 验证奖项数据
    if type(data.prizes) ~= "table" or next(data.prizes) == nil then
        TriggerClientEvent('lottery:notification', src, '奖项配置数据格式无效', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '奖项配置数据格式无效'
        })
        return
    end
    
    DebugPrint("验证通过，开始更新奖项配置")
    
    -- 更新配置
    local configUpdated = false
    if data.type == 'double_ball' then
        -- 更新双色球奖项配置
        for id, amount in pairs(data.prizes) do
            local prizeId = tonumber(id)
            if prizeId and Config.DoubleBall.prizes[prizeId] then
                DebugPrint("更新双色球奖项 " .. prizeId .. " 金额为: " .. amount)
                Config.DoubleBall.prizes[prizeId].amount = tonumber(amount)
                configUpdated = true
            end
        end
    elseif data.type == 'super_lotto' then
        -- 更新大乐透奖项配置
        for id, amount in pairs(data.prizes) do
            local prizeId = tonumber(id)
            if prizeId and Config.SuperLotto.prizes[prizeId] then
                DebugPrint("更新大乐透奖项 " .. prizeId .. " 金额为: " .. amount)
                Config.SuperLotto.prizes[prizeId].amount = tonumber(amount)
                configUpdated = true
            end
        end
    else
        TriggerClientEvent('lottery:notification', src, '无效的彩票类型', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '无效的彩票类型'
        })
        return
    end
    
    if not configUpdated then
        TriggerClientEvent('lottery:notification', src, '没有更新任何奖项配置', 'warning')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '没有更新任何奖项配置'
        })
        return
    end
    
    -- 记录管理员操作
    local adminName = GetPlayerName(src)
    LogAdminAction(src, adminName, 'update_prize_config', '更新彩票奖项配置: ' .. data.type, 0)
    
    -- 返回成功消息和更新后的配置
    TriggerClientEvent('lottery:notification', src, '彩票奖项配置已更新', 'success')
    TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
        success = true,
        config = GetLotteryConfigData()
    })
    
    -- 广播配置更新
    TriggerClientEvent('lottery:refreshLotteryConfig', -1, GetLotteryConfigData())

    -- 永久保存配置到config.lua文件
    SaveConfigToLuaFile()
    DebugPrint("奖项配置已永久保存")
end

-- 注意：不再使用JSON文件保存配置，直接使用config.lua中的配置
-- 配置修改会直接更新内存中的Config表，重启后会恢复到config.lua中的默认值


-- 保存配置到独立的覆盖文件
function SaveConfigToLuaFile()
    DebugPrint("开始保存配置覆盖...")

    local configOverride = {
        DoubleBall = {
            price = Config.DoubleBall.price,
            drawTime = Config.DoubleBall.drawTime,
            drawDays = Config.DoubleBall.drawDays,
            prizes = {}
        },
        SuperLotto = {
            price = Config.SuperLotto.price,
            drawTime = Config.SuperLotto.drawTime,
            drawDays = Config.SuperLotto.drawDays,
            prizes = {}
        },
        ScratchCards = {},
        ScratchSymbols = {}
    }

    -- 保存双色球奖项
    for id, prize in pairs(Config.DoubleBall.prizes) do
        configOverride.DoubleBall.prizes[id] = {
            name = prize.name,
            match = prize.match,
            amount = prize.amount,
            poolPercent = prize.poolPercent
        }
    end

    -- 保存大乐透奖项
    for id, prize in pairs(Config.SuperLotto.prizes) do
        configOverride.SuperLotto.prizes[id] = {
            name = prize.name,
            match = prize.match,
            amount = prize.amount,
            poolPercent = prize.poolPercent
        }
    end

    -- 保存刮刮乐配置
    for cardType, cardConfig in pairs(Config.ScratchCards) do
        configOverride.ScratchCards[cardType] = {
            name = cardConfig.name,
            price = cardConfig.price,
            maxPrize = cardConfig.maxPrize,
            amountRates = cardConfig.amountRates,
            rowAmountRates = cardConfig.rowAmountRates,
            winningAmountRates = cardConfig.winningAmountRates,
            winningItemRates = cardConfig.winningItemRates,  -- 添加5倍彩钻物品权重配置
            iconItemRates = cardConfig.iconItemRates  -- 添加乘风破浪图标物品权重配置
        }
    end

    -- 保存刮刮乐符号配置
    configOverride.ScratchSymbols = {
        xiRate = Config.ScratchSymbols.xiRate,
        xiXiRate = Config.ScratchSymbols.xiXiRate,
        allSpecialRate = Config.ScratchSymbols.allSpecialRate,
        fusongMatchRates = Config.ScratchSymbols.fusongMatchRates,
        yaocaiMatchRates = Config.ScratchSymbols.yaocaiMatchRates,
        caizuanMatchRates = Config.ScratchSymbols.caizuanMatchRates,  -- 添加5倍彩钻匹配数量权重配置
        caizuanDiamondRates = Config.ScratchSymbols.caizuanDiamondRates,  -- 添加5倍彩钻钻石图案数量权重配置
        zhongguofuMatchRates = Config.ScratchSymbols.zhongguofuMatchRates,  -- 添加中国福匹配数量权重配置
        zhongguofuFuRates = Config.ScratchSymbols.zhongguofuFuRates,  -- 添加中国福福符号数量权重配置
        chengfengSailRates = Config.ScratchSymbols.chengfengSailRates,  -- 添加乘风破浪⛵符号数量权重配置
        chengfengTornadoRates = Config.ScratchSymbols.chengfengTornadoRates  -- 添加乘风破浪🌪符号数量权重配置
    }

    -- 保存到JSON文件
    local jsonContent = json.encode(configOverride, {indent = true})
    DebugPrint("生成的JSON内容长度: " .. string.len(jsonContent))
    DebugPrint("JSON内容预览: " .. string.sub(jsonContent, 1, 200) .. "...")

    local success = SaveResourceFile(GetCurrentResourceName(), "config_override.json", jsonContent, -1)
    if success then
        SystemPrint("^2[彩票系统] ^7配置已永久保存到config_override.json文件")
    else
        SystemPrint("^1[彩票系统] ^7错误：无法保存配置覆盖文件")
    end
end

-- 加载配置覆盖
function LoadConfigOverride()
    DebugPrint("尝试加载配置覆盖文件...")

    local overrideContent = LoadResourceFile(GetCurrentResourceName(), "config_override.json")
    if not overrideContent then
        SystemPrint("^3[彩票系统] ^7使用config.lua中的默认配置，配置修改只在当前会话有效")
        return
    end

    DebugPrint("找到配置覆盖文件，内容长度: " .. string.len(overrideContent))

    local success, configOverride = pcall(json.decode, overrideContent)
    if not success or not configOverride then
        SystemPrint("^1[彩票系统] ^7配置覆盖文件格式错误，使用默认配置")
        return
    end

    DebugPrint("正在加载配置覆盖...")

    -- 应用双色球配置覆盖
    if configOverride.DoubleBall then
        if configOverride.DoubleBall.price then
            Config.DoubleBall.price = configOverride.DoubleBall.price
            DebugPrint("覆盖双色球价格: " .. configOverride.DoubleBall.price)
        end

        if configOverride.DoubleBall.drawTime then
            Config.DoubleBall.drawTime = configOverride.DoubleBall.drawTime
        end

        if configOverride.DoubleBall.drawDays then
            Config.DoubleBall.drawDays = configOverride.DoubleBall.drawDays
        end

        if configOverride.DoubleBall.prizes then
            for id, prize in pairs(configOverride.DoubleBall.prizes) do
                if Config.DoubleBall.prizes[tonumber(id)] then
                    Config.DoubleBall.prizes[tonumber(id)].amount = prize.amount
                    DebugPrint("覆盖双色球奖项 " .. id .. " 金额: " .. prize.amount)
                end
            end
        end
    end

    -- 应用大乐透配置覆盖
    if configOverride.SuperLotto then
        if configOverride.SuperLotto.price then
            Config.SuperLotto.price = configOverride.SuperLotto.price
            DebugPrint("覆盖大乐透价格: " .. configOverride.SuperLotto.price)
        end

        if configOverride.SuperLotto.drawTime then
            Config.SuperLotto.drawTime = configOverride.SuperLotto.drawTime
        end

        if configOverride.SuperLotto.drawDays then
            Config.SuperLotto.drawDays = configOverride.SuperLotto.drawDays
        end

        if configOverride.SuperLotto.prizes then
            for id, prize in pairs(configOverride.SuperLotto.prizes) do
                if Config.SuperLotto.prizes[tonumber(id)] then
                    Config.SuperLotto.prizes[tonumber(id)].amount = prize.amount
                    DebugPrint("覆盖大乐透奖项 " .. id .. " 金额: " .. prize.amount)
                end
            end
        end
    end

    -- 应用刮刮乐配置覆盖
    if configOverride.ScratchCards then
        for cardType, cardConfig in pairs(configOverride.ScratchCards) do
            if Config.ScratchCards[cardType] then
                if cardConfig.price then
                    Config.ScratchCards[cardType].price = cardConfig.price
                end
                if cardConfig.amountRates then
                    -- 确保键和值都是数字类型
                    local convertedRates = {}
                    for k, v in pairs(cardConfig.amountRates) do
                        convertedRates[tonumber(k)] = tonumber(v)
                    end
                    Config.ScratchCards[cardType].amountRates = convertedRates
                end
                if cardConfig.rowAmountRates then
                    -- 确保键和值都是数字类型
                    local convertedRates = {}
                    for k, v in pairs(cardConfig.rowAmountRates) do
                        convertedRates[tonumber(k)] = tonumber(v)
                    end
                    Config.ScratchCards[cardType].rowAmountRates = convertedRates
                end
                if cardConfig.winningAmountRates then
                    -- 确保键和值都是数字类型
                    local convertedRates = {}
                    for k, v in pairs(cardConfig.winningAmountRates) do
                        convertedRates[tonumber(k)] = tonumber(v)
                    end
                    Config.ScratchCards[cardType].winningAmountRates = convertedRates
                end
                if cardConfig.winningItemRates then
                    -- 物品权重配置，键是字符串，值是数字
                    local convertedRates = {}
                    for k, v in pairs(cardConfig.winningItemRates) do
                        convertedRates[tostring(k)] = tonumber(v)
                    end
                    Config.ScratchCards[cardType].winningItemRates = convertedRates
                    DebugPrint("覆盖5倍彩钻物品权重配置: " .. cardType)
                end
                if cardConfig.iconItemRates then
                    -- 乘风破浪图标物品权重配置，键是字符串，值是数字
                    local convertedRates = {}
                    for k, v in pairs(cardConfig.iconItemRates) do
                        convertedRates[tostring(k)] = tonumber(v)
                    end
                    Config.ScratchCards[cardType].iconItemRates = convertedRates
                    DebugPrint("覆盖乘风破浪图标物品权重配置: " .. cardType)
                end
                DebugPrint("覆盖刮刮乐配置: " .. cardType)
            end
        end
    end

    -- 应用刮刮乐符号配置覆盖
    if configOverride.ScratchSymbols then
        if configOverride.ScratchSymbols.xiRate then
            Config.ScratchSymbols.xiRate = configOverride.ScratchSymbols.xiRate
        end
        if configOverride.ScratchSymbols.xiXiRate then
            Config.ScratchSymbols.xiXiRate = configOverride.ScratchSymbols.xiXiRate
        end
        if configOverride.ScratchSymbols.allSpecialRate then
            Config.ScratchSymbols.allSpecialRate = configOverride.ScratchSymbols.allSpecialRate
        end
        if configOverride.ScratchSymbols.fusongMatchRates then
            -- 确保键和值都是数字类型
            local convertedRates = {}
            for k, v in pairs(configOverride.ScratchSymbols.fusongMatchRates) do
                convertedRates[tonumber(k)] = tonumber(v)
            end
            Config.ScratchSymbols.fusongMatchRates = convertedRates
        end
        if configOverride.ScratchSymbols.yaocaiMatchRates then
            -- 确保键和值都是数字类型
            local convertedRates = {}
            for k, v in pairs(configOverride.ScratchSymbols.yaocaiMatchRates) do
                convertedRates[tonumber(k)] = tonumber(v)
            end
            Config.ScratchSymbols.yaocaiMatchRates = convertedRates
        end
        if configOverride.ScratchSymbols.caizuanMatchRates then
            -- 确保键和值都是数字类型
            local convertedRates = {}
            for k, v in pairs(configOverride.ScratchSymbols.caizuanMatchRates) do
                convertedRates[tonumber(k)] = tonumber(v)
            end
            Config.ScratchSymbols.caizuanMatchRates = convertedRates
            DebugPrint("覆盖5倍彩钻匹配数量权重配置")
        end
        if configOverride.ScratchSymbols.caizuanDiamondRates then
            -- 确保键和值都是数字类型
            local convertedRates = {}
            for k, v in pairs(configOverride.ScratchSymbols.caizuanDiamondRates) do
                convertedRates[tonumber(k)] = tonumber(v)
            end
            Config.ScratchSymbols.caizuanDiamondRates = convertedRates
            DebugPrint("覆盖5倍彩钻钻石图案数量权重配置")
        end
        if configOverride.ScratchSymbols.zhongguofuMatchRates then
            -- 确保键和值都是数字类型
            local convertedRates = {}
            for k, v in pairs(configOverride.ScratchSymbols.zhongguofuMatchRates) do
                convertedRates[tonumber(k)] = tonumber(v)
            end
            Config.ScratchSymbols.zhongguofuMatchRates = convertedRates
            DebugPrint("覆盖中国福匹配数量权重配置")
        end
        if configOverride.ScratchSymbols.zhongguofuFuRates then
            -- 确保键和值都是数字类型
            local convertedRates = {}
            for k, v in pairs(configOverride.ScratchSymbols.zhongguofuFuRates) do
                convertedRates[tonumber(k)] = tonumber(v)
            end
            Config.ScratchSymbols.zhongguofuFuRates = convertedRates
            DebugPrint("覆盖中国福福符号数量权重配置")
        end
        if configOverride.ScratchSymbols.chengfengSailRates then
            -- 确保键和值都是数字类型
            local convertedRates = {}
            for k, v in pairs(configOverride.ScratchSymbols.chengfengSailRates) do
                convertedRates[tonumber(k)] = tonumber(v)
            end
            Config.ScratchSymbols.chengfengSailRates = convertedRates
            DebugPrint("覆盖乘风破浪⛵符号数量权重配置")
        end
        if configOverride.ScratchSymbols.chengfengTornadoRates then
            -- 确保键和值都是数字类型
            local convertedRates = {}
            for k, v in pairs(configOverride.ScratchSymbols.chengfengTornadoRates) do
                convertedRates[tonumber(k)] = tonumber(v)
            end
            Config.ScratchSymbols.chengfengTornadoRates = convertedRates
            DebugPrint("覆盖乘风破浪🌪符号数量权重配置")
        end
        DebugPrint("覆盖刮刮乐符号配置")
    end

    DebugPrint("配置覆盖加载完成")

    -- 验证配置覆盖是否生效
    DebugPrint("验证配置覆盖结果:")
    DebugPrint("当前双色球价格: " .. Config.DoubleBall.price)
    DebugPrint("当前双色球一等奖金额: " .. Config.DoubleBall.prizes[1].amount)
    DebugPrint("当前大乐透价格: " .. Config.SuperLotto.price)
end

-- 重置配置到默认值
RegisterNetEvent('lottery:resetConfigToDefault')
AddEventHandler('lottery:resetConfigToDefault', function()
    local src = source

    -- 删除配置覆盖文件
    SaveResourceFile(GetCurrentResourceName(), "config_override.json", "", -1)

    -- 重新加载资源以应用默认配置
    ExecuteCommand('refresh')
    ExecuteCommand('restart ' .. GetCurrentResourceName())

    SystemPrint("^2[彩票系统] ^7配置已重置为默认值，资源正在重启...")
end)



-- 保存刮刮乐配置函数已合并到SaveScratchRates函数中

-- 保存刮刮乐权重配置
function SaveScratchRates(source, data)
    local src = source
    
    -- 添加调试输出
    DebugPrint("收到刮刮乐权重配置请求: " .. json.encode(data))
    
    -- 检查是否启用可配置选项
    if not Config.LotteryConfig.configurable then
        TriggerClientEvent('lottery:notification', src, '彩票配置功能已禁用', 'error')
        TriggerClientEvent('lottery:nui:saveScratchRatesResult', src, {
            success = false,
            message = '彩票配置功能已禁用'
        })
        return
    end
    
    -- 检查权限
    local hasPermission = HasAdminPermission(src)
    if not hasPermission then
        TriggerClientEvent('lottery:notification', src, '您没有权限修改刮刮乐权重配置', 'error')
        TriggerClientEvent('lottery:nui:saveScratchRatesResult', src, {
            success = false,
            message = '权限不足'
        })
        return
    end
    
    -- 验证参数
    if not data.type or not data.ratesType or not data.rates then
        TriggerClientEvent('lottery:notification', src, '无效的权重配置参数', 'error')
        TriggerClientEvent('lottery:nui:saveScratchRatesResult', src, {
            success = false,
            message = '无效的权重配置参数'
        })
        return
    end
    
    DebugPrint("验证通过，开始更新刮刮乐配置")
    
    -- 更新配置
    if data.ratesType == 'config' then
        -- 处理基本配置更新
        if not Config.ScratchCards[data.type] then
            TriggerClientEvent('lottery:notification', src, '无效的刮刮乐类型', 'error')
            TriggerClientEvent('lottery:nui:saveScratchRatesResult', src, {
                success = false,
                message = '无效的刮刮乐类型'
            })
            return
        end
        
        -- 价格范围验证
        if data.rates.price and (data.rates.price < 0 or data.rates.price > 10000000) then
            TriggerClientEvent('lottery:notification', src, '价格必须在10到500之间', 'error')
            TriggerClientEvent('lottery:nui:saveScratchRatesResult', src, {
                success = false,
                message = '价格范围无效'
            })
            return
        end
        
        -- 最大奖金验证
        if data.rates.maxPrize and (data.rates.maxPrize < 1000 or data.rates.maxPrize > 10000000) then
            TriggerClientEvent('lottery:notification', src, '最大奖金必须在1000到10000000之间', 'error')
            TriggerClientEvent('lottery:nui:saveScratchRatesResult', src, {
                success = false,
                message = '最大奖金范围无效'
            })
            return
        end
        
        -- 喜符号概率验证
        if data.type == 'scratch_xixiangfeng' then
            if data.rates.xiRate and (data.rates.xiRate < 0 or data.rates.xiRate > 100) then
                TriggerClientEvent('lottery:notification', src, '喜符号出现概率必须在0到100之间', 'error')
                TriggerClientEvent('lottery:nui:saveScratchRatesResult', src, {
                    success = false,
                    message = '喜符号概率范围无效'
                })
                return
            end
            
            if data.rates.xiXiRate and (data.rates.xiXiRate < 0 or data.rates.xiXiRate > 100) then
                TriggerClientEvent('lottery:notification', src, '囍符号出现概率必须在0到100之间', 'error')
                TriggerClientEvent('lottery:nui:saveScratchRatesResult', src, {
                    success = false,
                    message = '囍符号概率范围无效'
                })
                return
            end
            
            if data.rates.allSpecialRate and (data.rates.allSpecialRate < 0 or data.rates.allSpecialRate > 100) then
                TriggerClientEvent('lottery:notification', src, '全部是喜出现的概率必须在0到100之间', 'error')
                TriggerClientEvent('lottery:nui:saveScratchRatesResult', src, {
                    success = false,
                    message = '全部是喜概率范围无效'
                })
                return
            end
        end
        
        -- 更新配置
        if data.rates.price then
            Config.ScratchCards[data.type].price = data.rates.price
        end
        if data.rates.maxPrize then
            Config.ScratchCards[data.type].maxPrize = data.rates.maxPrize
        end
        
        -- 更新喜符号概率配置
        if data.type == 'scratch_xixiangfeng' then
            if data.rates.xiRate then
                Config.ScratchSymbols.xiRate = tonumber(data.rates.xiRate)
            end
            if data.rates.xiXiRate then
                Config.ScratchSymbols.xiXiRate = tonumber(data.rates.xiXiRate)
            end
            if data.rates.allSpecialRate then
                Config.ScratchSymbols.allSpecialRate = tonumber(data.rates.allSpecialRate)
            end
        end
        
        -- 记录管理员操作
        local adminName = GetPlayerName(src)
        LogAdminAction(src, adminName, 'update_scratch_config', '更新刮刮乐配置: ' .. data.type, 0)
    elseif data.type == 'scratch_xixiangfeng' and data.ratesType == 'amountRates' then
        -- 更新喜相逢金额权重配置
        -- 确保所有键和值都是数字类型
        local convertedRates = {}
        for k, v in pairs(data.rates) do
            convertedRates[tonumber(k)] = tonumber(v)
        end
        Config.ScratchCards.scratch_xixiangfeng.amountRates = convertedRates
        
        -- 记录管理员操作
        local adminName = GetPlayerName(src)
        LogAdminAction(src, adminName, 'update_scratch_rates', '更新刮刮乐权重配置: ' .. data.type .. '.' .. data.ratesType, 0)
    elseif data.type == 'scratch_fusong' and data.ratesType == 'rowAmountRates' then
        -- 更新福鼠送彩行金额权重配置
        -- 确保所有键和值都是数字类型
        local convertedRates = {}
        for k, v in pairs(data.rates) do
            convertedRates[tonumber(k)] = tonumber(v)
        end
        Config.ScratchCards.scratch_fusong.rowAmountRates = convertedRates
        
        -- 记录管理员操作
        local adminName = GetPlayerName(src)
        LogAdminAction(src, adminName, 'update_scratch_rates', '更新刮刮乐权重配置: ' .. data.type .. '.' .. data.ratesType, 0)
    elseif data.type == 'scratch_symbols' and data.ratesType == 'fusongMatchRates' then
        -- 更新福鼠送彩匹配数量权重配置
        -- 确保所有键和值都是数字类型
        local convertedRates = {}
        for k, v in pairs(data.rates) do
            convertedRates[tonumber(k)] = tonumber(v)
        end
        Config.ScratchSymbols.fusongMatchRates = convertedRates
        
        -- 记录管理员操作
        local adminName = GetPlayerName(src)
        LogAdminAction(src, adminName, 'update_scratch_rates', '更新刮刮乐权重配置: ' .. data.type .. '.' .. data.ratesType, 0)
    elseif data.type == 'scratch_yaocai' and data.ratesType == 'winningAmountRates' then
        -- 更新耀出彩中奖号码金额权重配置
        -- 确保所有键和值都是数字类型
        local convertedRates = {}
        for k, v in pairs(data.rates) do
            convertedRates[tonumber(k)] = tonumber(v)
        end
        Config.ScratchCards.scratch_yaocai.winningAmountRates = convertedRates
        
        -- 记录管理员操作
        local adminName = GetPlayerName(src)
        LogAdminAction(src, adminName, 'update_scratch_rates', '更新刮刮乐权重配置: ' .. data.type .. '.' .. data.ratesType, 0)
    elseif data.type == 'scratch_symbols' and data.ratesType == 'yaocaiMatchRates' then
        -- 更新耀出彩匹配数量权重配置
        -- 确保所有键和值都是数字类型
        local convertedRates = {}
        for k, v in pairs(data.rates) do
            convertedRates[tonumber(k)] = tonumber(v)
        end
        Config.ScratchSymbols.yaocaiMatchRates = convertedRates

        -- 记录管理员操作
        local adminName = GetPlayerName(src)
        LogAdminAction(src, adminName, 'update_scratch_rates', '更新刮刮乐权重配置: ' .. data.type .. '.' .. data.ratesType, 0)
    elseif data.type == 'scratch_caizuan' and data.ratesType == 'winningItemRates' then
        -- 更新5倍彩钻中奖物品权重配置
        -- 物品权重配置，键是字符串，值是数字
        local convertedRates = {}
        for k, v in pairs(data.rates) do
            convertedRates[tostring(k)] = tonumber(v)
        end
        Config.ScratchCards.scratch_caizuan.winningItemRates = convertedRates

        -- 记录管理员操作
        local adminName = GetPlayerName(src)
        LogAdminAction(src, adminName, 'update_scratch_rates', '更新刮刮乐权重配置: ' .. data.type .. '.' .. data.ratesType, 0)
    elseif data.type == 'scratch_symbols' and data.ratesType == 'caizuanMatchRates' then
        -- 更新5倍彩钻匹配数量权重配置
        -- 确保所有键和值都是数字类型
        local convertedRates = {}
        for k, v in pairs(data.rates) do
            convertedRates[tonumber(k)] = tonumber(v)
        end
        Config.ScratchSymbols.caizuanMatchRates = convertedRates

        -- 记录管理员操作
        local adminName = GetPlayerName(src)
        LogAdminAction(src, adminName, 'update_scratch_rates', '更新刮刮乐权重配置: ' .. data.type .. '.' .. data.ratesType, 0)
    elseif data.type == 'scratch_symbols' and data.ratesType == 'caizuanDiamondRates' then
        -- 更新5倍彩钻钻石图案数量权重配置
        -- 确保所有键和值都是数字类型
        local convertedRates = {}
        for k, v in pairs(data.rates) do
            convertedRates[tonumber(k)] = tonumber(v)
        end
        Config.ScratchSymbols.caizuanDiamondRates = convertedRates

        -- 记录管理员操作
        local adminName = GetPlayerName(src)
        LogAdminAction(src, adminName, 'update_scratch_rates', '更新刮刮乐权重配置: ' .. data.type .. '.' .. data.ratesType, 0)
    elseif data.type == 'scratch_zhongguofu' and data.ratesType == 'winningItemRates' then
        -- 更新中国福刮刮乐物品权重配置
        -- 确保所有键是字符串，值是数字
        local convertedRates = {}
        for k, v in pairs(data.rates) do
            convertedRates[tostring(k)] = tonumber(v)
        end
        Config.ScratchCards.scratch_zhongguofu.winningItemRates = convertedRates

        -- 记录管理员操作
        local adminName = GetPlayerName(src)
        LogAdminAction(src, adminName, 'update_scratch_rates', '更新刮刮乐权重配置: ' .. data.type .. '.' .. data.ratesType, 0)
    elseif data.type == 'scratch_symbols' and data.ratesType == 'zhongguofuMatchRates' then
        -- 更新中国福匹配数量权重配置
        -- 确保所有键和值都是数字类型
        local convertedRates = {}
        for k, v in pairs(data.rates) do
            convertedRates[tonumber(k)] = tonumber(v)
        end
        Config.ScratchSymbols.zhongguofuMatchRates = convertedRates

        -- 记录管理员操作
        local adminName = GetPlayerName(src)
        LogAdminAction(src, adminName, 'update_scratch_rates', '更新刮刮乐权重配置: ' .. data.type .. '.' .. data.ratesType, 0)
    elseif data.type == 'scratch_symbols' and data.ratesType == 'zhongguofuFuRates' then
        -- 更新中国福福符号数量权重配置
        -- 确保所有键和值都是数字类型
        local convertedRates = {}
        for k, v in pairs(data.rates) do
            convertedRates[tonumber(k)] = tonumber(v)
        end
        Config.ScratchSymbols.zhongguofuFuRates = convertedRates

        -- 记录管理员操作
        local adminName = GetPlayerName(src)
        LogAdminAction(src, adminName, 'update_scratch_rates', '更新刮刮乐权重配置: ' .. data.type .. '.' .. data.ratesType, 0)
    elseif data.type == 'scratch_chengfeng' and data.ratesType == 'iconItemRates' then
        -- 更新乘风破浪图标物品权重配置
        -- 确保所有键是字符串，值是数字
        local convertedRates = {}
        for k, v in pairs(data.rates) do
            convertedRates[tostring(k)] = tonumber(v)
        end
        Config.ScratchCards.scratch_chengfeng.iconItemRates = convertedRates

        -- 记录管理员操作
        local adminName = GetPlayerName(src)
        LogAdminAction(src, adminName, 'update_scratch_rates', '更新刮刮乐权重配置: ' .. data.type .. '.' .. data.ratesType, 0)
    elseif data.type == 'scratch_chengfeng' and data.ratesType == 'winningItemRates' then
        -- 更新乘风破浪中奖符号物品权重配置
        -- 确保所有键是字符串，值是数字
        local convertedRates = {}
        for k, v in pairs(data.rates) do
            convertedRates[tostring(k)] = tonumber(v)
        end
        Config.ScratchCards.scratch_chengfeng.winningItemRates = convertedRates

        -- 记录管理员操作
        local adminName = GetPlayerName(src)
        LogAdminAction(src, adminName, 'update_scratch_rates', '更新刮刮乐权重配置: ' .. data.type .. '.' .. data.ratesType, 0)
    elseif data.type == 'scratch_symbols' and data.ratesType == 'chengfengSailRates' then
        -- 更新乘风破浪⛵符号数量权重配置
        -- 确保所有键和值都是数字类型
        local convertedRates = {}
        for k, v in pairs(data.rates) do
            convertedRates[tonumber(k)] = tonumber(v)
        end
        Config.ScratchSymbols.chengfengSailRates = convertedRates

        -- 记录管理员操作
        local adminName = GetPlayerName(src)
        LogAdminAction(src, adminName, 'update_scratch_rates', '更新刮刮乐权重配置: ' .. data.type .. '.' .. data.ratesType, 0)
    elseif data.type == 'scratch_symbols' and data.ratesType == 'chengfengTornadoRates' then
        -- 更新乘风破浪🌪符号数量权重配置
        -- 确保所有键和值都是数字类型
        local convertedRates = {}
        for k, v in pairs(data.rates) do
            convertedRates[tonumber(k)] = tonumber(v)
        end
        Config.ScratchSymbols.chengfengTornadoRates = convertedRates

        -- 记录管理员操作
        local adminName = GetPlayerName(src)
        LogAdminAction(src, adminName, 'update_scratch_rates', '更新刮刮乐权重配置: ' .. data.type .. '.' .. data.ratesType, 0)
    else
        TriggerClientEvent('lottery:notification', src, '无效的刮刮乐权重类型', 'error')
        TriggerClientEvent('lottery:nui:saveScratchRatesResult', src, {
            success = false,
            message = '无效的刮刮乐权重类型'
        })
        return
    end
    
    -- 返回成功消息和更新后的配置
    TriggerClientEvent('lottery:notification', src, '刮刮乐配置已更新', 'success')
    TriggerClientEvent('lottery:nui:saveScratchRatesResult', src, {
        success = true,
        config = GetLotteryConfigData()
    })
    
    -- 广播配置更新
    TriggerClientEvent('lottery:refreshLotteryConfig', -1, GetLotteryConfigData())

    -- 永久保存配置到config.lua文件
    SaveConfigToLuaFile()
    DebugPrint("刮刮乐配置已永久保存")
end

-- 保存开奖设置
function SaveDrawSettings(source, data)
    local src = source
    
    -- 检查是否启用可配置选项
    if not Config.LotteryConfig.configurable then
        TriggerClientEvent('lottery:notification', src, '彩票配置功能已禁用', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '彩票配置功能已禁用'
        })
        return
    end
    
    -- 检查权限
    local hasPermission = HasAdminPermission(src)
    if not hasPermission then
        TriggerClientEvent('lottery:notification', src, '您没有权限修改开奖设置', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '权限不足'
        })
        return
    end
    
    -- 验证参数
    if not data.type or not data.drawTime or not data.drawDays then
        TriggerClientEvent('lottery:notification', src, '无效的开奖设置参数', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '无效的开奖设置参数'
        })
        return
    end
    
    -- 验证开奖时间
    if not data.drawTime.hour or not data.drawTime.minute or 
       data.drawTime.hour < 0 or data.drawTime.hour > 23 or 
       data.drawTime.minute < 0 or data.drawTime.minute > 59 then
        TriggerClientEvent('lottery:notification', src, '无效的开奖时间', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '无效的开奖时间'
        })
        return
    end
    
    -- 验证开奖日期
    if #data.drawDays == 0 then
        TriggerClientEvent('lottery:notification', src, '请至少选择一个开奖日期', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '请至少选择一个开奖日期'
        })
        return
    end
    
    -- 更新配置
    if data.type == 'double_ball' then
        Config.DoubleBall.drawTime = {
            hour = data.drawTime.hour,
            minute = data.drawTime.minute
        }
        Config.DoubleBall.drawDays = data.drawDays
    elseif data.type == 'super_lotto' then
        Config.SuperLotto.drawTime = {
            hour = data.drawTime.hour,
            minute = data.drawTime.minute
        }
        Config.SuperLotto.drawDays = data.drawDays
    else
        TriggerClientEvent('lottery:notification', src, '无效的彩票类型', 'error')
        TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
            success = false,
            message = '无效的彩票类型'
        })
        return
    end
    
    -- 记录管理员操作
    local adminName = GetPlayerName(src)
    LogAdminAction(src, adminName, 'update_draw_settings', '更新开奖设置: ' .. data.type, 0)
    
    -- 返回成功消息和更新后的配置
    TriggerClientEvent('lottery:notification', src, '开奖设置已更新', 'success')
    TriggerClientEvent('lottery:nui:saveLotteryConfigResult', src, {
        success = true,
        config = GetLotteryConfigData()
    })
    
    -- 广播配置更新
    TriggerClientEvent('lottery:refreshLotteryConfig', -1, GetLotteryConfigData())

    -- 永久保存配置到config.lua文件
    SaveConfigToLuaFile()
    DebugPrint("开奖设置已永久保存")
end