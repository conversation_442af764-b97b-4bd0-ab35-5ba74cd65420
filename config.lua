Config = {}

-- 框架配置
Config.Framework = 'ESX' -- 将在运行时自动检测，可以手动设置为 'ESX' 或 'QB'

-- 彩票配置系统
Config.LotteryConfig = {
    configurable = true,      -- 是否允许通过管理系统配置彩票
    minPrice = 0,           -- 最低彩票价格
    maxPrice = 10000000,         -- 最高彩票价格
    minProbability = 0.001,  -- 最低中奖概率(0.1%)
    maxProbability = 0.5     -- 最高中奖概率(50%)
}

-- 数据库配置
Config.Database = {
    updateInterval = 60000, -- 数据库更新间隔（毫秒）
}

-- 缓存配置
Config.Cache = {
    enabled = true,       -- 是否启用缓存系统
    ttl = 30,            -- 缓存有效期（秒）
    clearOnSave = true,  -- 保存数据时是否清除相关缓存
    statsInterval = 10,   -- 统计信息打印间隔（分钟）
    excludeScratch = true -- 是否排除刮刮乐缓存
}

-- 批量插入队列配置
Config.InsertQueue = {
    enabled = true,        -- 启用批量插入队列，处理高并发
    interval = 5000,       -- 批量插入间隔（毫秒）- 5秒间隔
    maxSize = 30,          -- 队列最大长度，达到此长度会触发批量插入
    batchSize = {          -- 每种类型的批量插入数量
        double_ball = 5,   -- 双色球每批次最多插入数量
        super_lotto = 5,   -- 大乐透每批次最多插入数量
    },
    debug = false          -- 是否打印队列调试信息
}

-- 彩票店配置
Config.Shops = {
    [1] = {
        name = "中心彩票店",
        coords = vector3(373.875, 325.896, 103.566),
        heading = 255.118,
        createPed = true, -- 是否创建NPC
        createBlip = true, -- 是否创建地图标记
        blip = {
            sprite = 500,
            color = 46,
            scale = 0.8,
            name = "彩票店"
        },
        ped = {
            model = "a_m_m_business_01",
            coords = vector3(373.875, 325.896, 102.566),
            heading = 255.118,
            scenario = "WORLD_HUMAN_CLIPBOARD"
        }
    }
}

-- 刮刮乐配置
Config.ScratchCards = {
    scratch_xixiangfeng = {
        name = "喜相逢",
        price = 100,
        maxPrize = 300000,
        image = "xixiangfeng.png",
        completedImage = "xixiangfeng1.png",
        regularAmounts = {5, 10, 20, 50, 100, 200, 500, 1000, 5000, 10000,50000,300000}, -- 普通符号可能出现的金额
        specialAmounts = {5, 10, 20, 50, 100, 200, 500, 1000, 5000, 10000,50000,300000}, -- 喜囍符号可能出现的金额（未设置amountRates时使用）
		--金额出现在喜下的概率 数值代表的是权重而不是严格的百分比
		--只要保持各个金额之间的相对比例符合您期望的概率分布即可。
		--例如，5元的权重是30，300000元的权重是0.01，意味着5元出现的概率是300000元的3000倍。
        amountRates = {
            [5] = 30,      -- 30% 概率显示5元
            [10] = 25,     -- 25% 概率显示10元
            [20] = 15,     -- 15% 概率显示20元
            [50] = 10,     -- 10% 概率显示50元
            [100] = 8,     -- 8% 概率显示100元
            [200] = 5,     -- 5% 概率显示200元
            [500] = 3,     -- 3% 概率显示500元
            [1000] = 2,    -- 2% 概率显示1000元
            [5000] = 1.5,  -- 1.5% 概率显示5000元
            [10000] = 0.5 , -- 0.5% 概率显示10000元
            [50000] = 0.1, -- 0.1% 概率显示50000元
            [300000] = 0.01, -- 0.01% 概率显示300000元
        }
    },
    scratch_fusong = {
        name = "福鼠送彩",
        price = 1000,
        maxPrize = 250000,
        image = "fusong.png",
        completedImage = "fusong1.png",
        rowPrizes = {10, 20, 50, 100, 500}, -- 旧版每行的奖金值，保留向后兼容
        -- 行金额权重配置，类似喜相逢的amountRates
        rowAmountRates = {
            [10] = 30,     -- 30% 概率显示10元
            [20] = 25,     -- 25% 概率显示20元
            [50] = 20,     -- 20% 概率显示50元
            [100] = 15,    -- 15% 概率显示100元
            [200] = 8,     -- 8% 概率显示200元
            [500] = 5,     -- 5% 概率显示500元
            [1000] = 3,    -- 3% 概率显示1000元
            [2000] = 2,    -- 2% 概率显示2000元
            [5000] = 1,    -- 1% 概率显示5000元
            [10000] = 0.5, -- 0.5% 概率显示10000元
            [50000] = 0.1, -- 0.1% 概率显示50000元
            [250000] = 0.05, -- 0.05% 概率显示250000元
        }
    },
    scratch_yaocai = {
        name = "耀出彩",
        price = 200,
        maxPrize = 800000,
        image = "yaocai.png",
        completedImage = "yaocai1.png",
        -- 耀出彩中奖号码金额权重配置
        winningAmountRates = {
            [50] = 30,     -- 30% 概率显示50元
            [100] = 25,    -- 25% 概率显示100元
            [200] = 20,    -- 20% 概率显示200元
            [500] = 10,    -- 10% 概率显示500元
            [1000] = 8,    -- 8% 概率显示1000元
            [2000] = 5,    -- 5% 概率显示2000元
            [5000] = 3,    -- 3% 概率显示5000元
            [10000] = 1,   -- 1% 概率显示10000元
            [50000] = 0.2, -- 0.2% 概率显示50000元
            [200000] = 0.1,
            [500000] = 0.02, -- 0.1% 概率显示200000元
            [800000] = 0.001 -- 0.1% 概率显示200000元
        }
    },
    scratch_caizuan = {
        name = "5倍彩钻",
        price = 300,
        maxPrize = 1000000,
        image = "caizuan.png",
        completedImage = "caizuan1.png",
        -- 5倍彩钻中奖号码物品权重配置
        winningItemRates = {
            ["bread"] = 30,        -- 30% 概率显示面包
            ["water"] = 25,        -- 25% 概率显示水
            ["bandage"] = 20,      -- 20% 概率显示绷带
            ["phone"] = 10,        -- 10% 概率显示手机
            ["lockpick"] = 8,      -- 8% 概率显示撬锁器
            ["diamond"] = 5,       -- 5% 概率显示钻石
            ["gold"] = 3,          -- 3% 概率显示金条
            ["weapon_pistol"] = 1, -- 1% 概率显示手枪
            ["money:1000"] = 0.5,  -- 0.5% 概率显示1000现金
            ["car_key"] = 0.2      -- 0.2% 概率显示车钥匙
            
        }
    },
    scratch_zhongguofu = {
        name = "中国福",
        price = 250,
        maxPrize = 800000,
        image = "fu.png",
        completedImage = "fu1.png",
        -- 中国福中奖号码物品权重配置
        winningItemRates = {
            ["bread"] = 30,        -- 30% 概率显示面包
            ["water"] = 25,        -- 25% 概率显示水
            ["bandage"] = 20,      -- 20% 概率显示绷带
            ["phone"] = 10,        -- 10% 概率显示手机
            ["lockpick"] = 8,      -- 8% 概率显示撬锁器
            ["diamond"] = 5,       -- 5% 概率显示钻石
            ["gold"] = 3,          -- 3% 概率显示金条
            ["weapon_pistol"] = 1, -- 1% 概率显示手枪
            ["money:800"] = 0.5,   -- 0.5% 概率显示800现金
            ["car_key"] = 0.2      -- 0.2% 概率显示车钥匙
        }
    },
    scratch_chengfeng = {
        name = "乘风破浪",
        price = 300,
        maxPrize = 1000000,
        image = "chengfeng.png",
        completedImage = "chengfeng1.png",
        -- 乘风破浪中奖符号物品权重配置（⛵ 和 🌪 符号下的物品）
        winningItemRates = {
            ["bread"] = 25,        -- 25% 概率显示面包
            ["water"] = 20,        -- 20% 概率显示水
            ["bandage"] = 18,      -- 18% 概率显示绷带
            ["phone"] = 12,        -- 12% 概率显示手机
            ["lockpick"] = 10,     -- 10% 概率显示撬锁器
            ["diamond"] = 8,       -- 8% 概率显示钻石
            ["gold"] = 5,          -- 5% 概率显示金条
            ["weapon_pistol"] = 2, -- 2% 概率显示手枪
            ["money:1200"] = 0.5, -- 0.5% 概率显示1200现金
            ["car_key"] = 0.3      -- 0.3% 概率显示车钥匙
        },
        -- 乘风破浪非中奖符号物品配置（随机显示，不使用权重）
        iconItemRates = {
            ["bread"] = 1,         -- 面包
            ["water"] = 1,         -- 水
            ["bandage"] = 1,       -- 绷带
            ["phone"] = 1,         -- 手机
            ["lockpick"] = 1,      -- 撬锁器
            ["diamond"] = 1,       -- 钻石
            ["gold"] = 1,          -- 金条
            ["weapon_pistol"] = 1, -- 手枪
            ["money:500"] = 1,     -- 500现金
            ["car_key"] = 1        -- 车钥匙
        }
    }
}

Config.ScratchSymbols = {
    xiRate = 10,           -- 喜符号出现概率 (%)
    xiXiRate = 2,          -- 囍符号出现概率 (%)
    allSpecialRate = 0,     -- 全部图案都为喜和囍的概率 (%)
    
    -- 福鼠送彩配置
    fusongMatchRates = {
        [0] = 160,   -- 出现0个福鼠号码的概率(权重)
        [1] = 100,   -- 出现1个福鼠号码的概率(权重)
        [2] = 80,   -- 出现2个福鼠号码的概率(权重)
        [3] = 20,   -- 出现3个福鼠号码的概率(权重)
        [4] = 15,   -- 出现4个福鼠号码的概率(权重)
        [5] = 10,   -- 出现5个福鼠号码的概率(权重)
        [6] = 8,    -- 出现6个福鼠号码的概率(权重)
        [7] = 6,    -- 出现7个福鼠号码的概率(权重)
        [8] = 5,    -- 出现8个福鼠号码的概率(权重)
        [9] = 0.5,    -- 出现9个福鼠号码的概率(权重)
        [10] = 0.4,   -- 出现10个福鼠号码的概率(权重)
        [15] = 0.3,   -- 出现15个福鼠号码的概率(权重)
        [20] = 0.2,   -- 出现20个福鼠号码的概率(权重)
        [25] = 0.1, -- 出现25个福鼠号码的概率(权重)
    },
    
    -- 福鼠送彩无中奖号码行的金额概率配置
    fusongNoMatchAmountRates = {
        highAmount = 20,     -- 超大奖金额出现概率 (%)
        mediumAmount = 30,  -- 中等奖金额出现概率 (%)
        lowAmount = 50,     -- 小额奖金额出现概率 (%)
    },
    
    -- 福鼠送彩无中奖号码行可能出现的金额范围
    fusongNoMatchAmounts = {
        low = {10, 20, 50, 100, 200},             -- 小额奖金范围
        medium = {500, 1000, 2000, 5000},         -- 中等奖金范围
        high = {10000, 50000, 100000, 250000}     -- 超大奖金范围
    },
    
    -- 耀出彩金额概率配置
    yaocaiAmountRates = {
        winningHighRate = 20,   -- 中奖号码高额奖金概率 (%)
        nonWinningHighRate = 5, -- 非中奖号码高额奖金概率 (%)
        nonWinningMediumRate = 20, -- 非中奖号码中等奖金概率 (%)
    },
    
    -- 耀出彩可能出现的金额范围
    yaocaiAmounts = {
        low = {10, 20, 50, 100, 200},          -- 小额奖金范围
        medium = {500, 1000, 2000},            -- 中等奖金范围
        high = {5000, 10000, 20000, 50000}     -- 高额奖金范围
    },
    
    -- 耀出彩中奖号码数量概率配置
    yaocaiMatchRates = {
        [0] = 160,     -- 出现0个中奖号码的概率(权重)
        [1] = 100,     -- 出现1个中奖号码的概率(权重)
        [2] = 25,     -- 出现2个中奖号码的概率(权重)
        [3] = 15,     -- 出现3个中奖号码的概率(权重)
        [4] = 10,     -- 出现4个中奖号码的概率(权重)
        [5] = 6,      -- 出现5个中奖号码的概率(权重)
        [6] = 4,      -- 出现6个中奖号码的概率(权重)
        [7] = 3,      -- 出现7个中奖号码的概率(权重)
        [8] = 2,      -- 出现8个中奖号码的概率(权重)
        [9] = 1,      -- 出现9个中奖号码的概率(权重)
        [10] = 0.5,   -- 出现10个中奖号码的概率(权重)
        [15] = 0.25,  -- 出现15个中奖号码的概率(权重)
        [20] = 0.1,   -- 出现20个中奖号码的概率(权重)
        [25] = 0.05,  -- 出现25个中奖号码的概率(权重)
    },
    
    -- 以下配置将被废弃，保留向后兼容
    fusongWinNumRate = 100,  -- 福鼠号码出现的概率 (%)
    fusongAllMatchRate = 1, -- 全部都是福鼠号码的概率 (%)
    fusongMaxMatches = 5,   -- 最大匹配数量
    
    -- 耀出彩配置
    yaocaiAllMatchRate = 0, -- 全部都是中奖号码的概率 (%)，设置为0禁用全匹配模式 别更改

    -- 5倍彩钻中奖号码数量概率配置
    caizuanMatchRates = {
        [0] = 150,     -- 出现0个中奖号码的概率(权重)
        [1] = 120,     -- 出现1个中奖号码的概率(权重)
        [2] = 80,      -- 出现2个中奖号码的概率(权重)
        [3] = 50,      -- 出现3个中奖号码的概率(权重)
        [4] = 30,      -- 出现4个中奖号码的概率(权重)
        [5] = 20,      -- 出现5个中奖号码的概率(权重)
        [6] = 15,      -- 出现6个中奖号码的概率(权重)
        [7] = 10,      -- 出现7个中奖号码的概率(权重)
        [8] = 8,       -- 出现8个中奖号码的概率(权重)
        [9] = 5,       -- 出现9个中奖号码的概率(权重)
        [10] = 3,      -- 出现10个中奖号码的概率(权重)
        [15] = 1,      -- 出现15个中奖号码的概率(权重)
        [20] = 0.5,    -- 出现20个中奖号码的概率(权重)
    },

    -- 5倍彩钻钻石图案出现概率配置
    caizuanDiamondRates = {
        [0] = 200,     -- 出现0个钻石图案的概率(权重)
        [1] = 8000,      -- 出现1个钻石图案的概率(权重)
        [2] = 30,      -- 出现2个钻石图案的概率(权重)
        [3] = 15,      -- 出现3个钻石图案的概率(权重)
        [4] = 8,       -- 出现4个钻石图案的概率(权重)
        [5] = 3,       -- 出现5个钻石图案的概率(权重)
    },

    -- 中国福中奖号码数量概率配置
    zhongguofuMatchRates = {
        [0] = 150,     -- 出现0个中奖号码的概率(权重)
        [1] = 120,     -- 出现1个中奖号码的概率(权重)
        [2] = 80,      -- 出现2个中奖号码的概率(权重)
        [3] = 50,      -- 出现3个中奖号码的概率(权重)
        [4] = 30,      -- 出现4个中奖号码的概率(权重)
        [5] = 20,      -- 出现5个中奖号码的概率(权重)
        [6] = 15,      -- 出现6个中奖号码的概率(权重)
        [7] = 10,      -- 出现7个中奖号码的概率(权重)
        [8] = 8,       -- 出现8个中奖号码的概率(权重)
        [9] = 5,       -- 出现9个中奖号码的概率(权重)
        [10] = 3,      -- 出现10个中奖号码的概率(权重)
        [11] = 2,      -- 出现11个中奖号码的概率(权重)
        [12] = 1.5,    -- 出现12个中奖号码的概率(权重)
        [13] = 1,      -- 出现13个中奖号码的概率(权重)
        [14] = 0.8,    -- 出现14个中奖号码的概率(权重)
        [15] = 0.5,    -- 出现15个中奖号码的概率(权重)
    },

    -- 中国福福符号图案出现概率配置
    zhongguofuFuRates = {
        [0] = 200,     -- 出现0个福符号图案的概率(权重)
        [1] = 8000,      -- 出现1个福符号图案的概率(权重)
        [2] = 30,      -- 出现2个福符号图案的概率(权重)
        [3] = 15,      -- 出现3个福符号图案的概率(权重)
        [4] = 8,       -- 出现4个福符号图案的概率(权重)
        [5] = 3,       -- 出现5个福符号图案的概率(权重)
    },

    -- 乘风破浪⛵符号图案出现概率配置
    chengfengSailRates = {
        [0] = 50,      -- 出现0个⛵符号图案的概率(权重)
        [1] = 100,     -- 出现1个⛵符号图案的概率(权重)
        [2] = 80,      -- 出现2个⛵符号图案的概率(权重)
        [3] = 60,      -- 出现3个⛵符号图案的概率(权重)
        [4] = 40,      -- 出现4个⛵符号图案的概率(权重)
        [5] = 30,      -- 出现5个⛵符号图案的概率(权重)
        [6] = 20,      -- 出现6个⛵符号图案的概率(权重)
        [7] = 15,      -- 出现7个⛵符号图案的概率(权重)
        [8] = 10,      -- 出现8个⛵符号图案的概率(权重)
        [9] = 8,       -- 出现9个⛵符号图案的概率(权重)
        [10] = 5,      -- 出现10个⛵符号图案的概率(权重)
    },

    -- 乘风破浪�符号图案出现概率配置
    chengfengTornadoRates = {
        [0] = 300,     -- 出现0个�符号图案的概率(权重)
        [1] = 150,     -- 出现1个�符号图案的概率(权重)
        [2] = 50,      -- 出现2个�符号图案的概率(权重)
        [3] = 20,      -- 出现3个�符号图案的概率(权重)
        [4] = 8,       -- 出现4个�符号图案的概率(权重)
        [5] = 3,       -- 出现5个�符号图案的概率(权重)
    },


}

-- 双色球配置
Config.DoubleBall = {
    name = "双色球",
    price = 2000,
    redBalls = 33,
    blueBalls = 16,
    selectRed = 6,
    selectBlue = 1,
    drawDays = {1, 2, 3, 4, 5, 6, 7}, -- 每天开奖
    drawTime = {hour = 07, minute = 48}, -- 晚上9:00开奖
    checkDrawToday = false, -- 是否检查今天是否已开奖
    prizes = {
        [1] = {match = {6, 1}, amount = 5000000, poolPercent = 0.5, name = "一等奖"},  -- 6红+1蓝，固定500万+奖池50%
        [2] = {match = {6, 0}, amount = 500000, poolPercent = 0.1, name = "二等奖"},   -- 6红，固定50万+奖池10%
        [3] = {match = {5, 1}, amount = 3000, name = "三等奖"},     -- 5红+1蓝
        [4] = {match = {5, 0}, amount = 200, name = "四等奖"},      -- 5红
        [5] = {match = {4, 1}, amount = 200, name = "四等奖"},      -- 4红+1蓝
        [6] = {match = {4, 0}, amount = 10, name = "五等奖"},       -- 4红
        [7] = {match = {3, 1}, amount = 10, name = "五等奖"},       -- 3红+1蓝
        [8] = {match = {2, 1}, amount = 5, name = "六等奖"},        -- 2红+1蓝
        [9] = {match = {1, 1}, amount = 5, name = "六等奖"},        -- 1红+1蓝
        [10] = {match = {0, 1}, amount = 5, name = "六等奖"}        -- 0红+1蓝
    }
}

-- 大乐透配置
Config.SuperLotto = {
    name = "大乐透",
    price = 200,
    frontBalls = 35,
    backBalls = 12,
    selectFront = 5,
    selectBack = 2,
    drawDays = {1, 2, 3, 4, 5, 6, 7}, -- 每天开奖
    drawTime = {hour = 07, minute = 48}, -- 晚上8:00开奖
    checkDrawToday = false, -- 是否检查今天是否已开奖
    prizes = {
        [1] = {match = {5, 2}, amount = 5000000, poolPercent = 0.5, name = "一等奖"}, -- 5前+2后，固定500万+奖池50%
        [2] = {match = {5, 1}, amount = 500000, poolPercent = 0.1, name = "二等奖"},   -- 5前+1后，固定50万+奖池10%
        [3] = {match = {5, 0}, amount = 10000, name = "三等奖"},    -- 5前
        [4] = {match = {4, 2}, amount = 3000, name = "四等奖"},     -- 4前+2后
        [5] = {match = {4, 1}, amount = 300, name = "五等奖"},      -- 4前+1后
        [6] = {match = {3, 2}, amount = 300, name = "五等奖"},      -- 3前+2后
        [7] = {match = {4, 0}, amount = 20, name = "六等奖"},       -- 4前
        [8] = {match = {3, 1}, amount = 20, name = "六等奖"},       -- 3前+1后
        [9] = {match = {2, 2}, amount = 20, name = "六等奖"},       -- 2前+2后
        [10] = {match = {3, 0}, amount = 5, name = "七等奖"},       -- 3前
        [11] = {match = {1, 2}, amount = 5, name = "七等奖"},       -- 1前+2后
        [12] = {match = {2, 1}, amount = 5, name = "七等奖"},       -- 2前+1后
        [13] = {match = {0, 2}, amount = 5, name = "八等奖"}        -- 0前+2后
    }
}

-- 奖池配置
Config.PrizePools = {
    double_ball = {
        initial = 5000000,    -- 初始奖池500万
        contribution = 0.50,  -- 每注贡献50%到奖池
        maxRollover = 1000      -- 最大滚存10期
    },
    super_lotto = {
        initial = 5000000,   -- 初始奖池1000万
        contribution = 0.50,  -- 每注贡献50%到奖池
        maxRollover = 1000      -- 最大滚存15期
    }
}

-- 资金流向配置
Config.MoneyFlow = {
    lottery = {
        prizePool = 0.50,     -- 50%彩票购买金额进入奖池
        shopAccount = 0.50    -- 50%彩票购买金额进入彩票店账户
    },
    scratchCard = {
        shopAccount = 1.0     -- 100%刮刮乐购买金额进入彩票店账户
    }
}

-- UI配置
Config.UI = {
    soundEffects = true,
    animations = true,
    autoClaimOnReveal = true, -- 刮刮乐全部显示后自动兑奖
}

-- 刮刮乐符号配置


-- 货币配置
Config.Currency = {
    symbol = "¥",
    name = "money" -- ESX: money, QB: cash
}

-- 物品配置 (如果使用物品系统)
Config.Items = {
    useScratchItems = true, -- 是否使用刮刮乐物品
    scratchItems = {
        scratch_xixiangfeng = "scratch_card_xixiangfeng",
        scratch_fusong = "scratch_card_fusong",
        scratch_yaocai = "scratch_card_yaocai",
        scratch_caizuan = "scratch_card_caizuan",
        scratch_zhongguofu = "scratch_card_zhongguofu",
        scratch_chengfeng = "scratch_card_chengfeng"
    },
    -- 物品名称映射配置
    itemNames = {
        -- 基础物品
        ["bread"] = "面包",
        ["water"] = "水",
        ["bandage"] = "绷带",
        ["phone"] = "手机",
        ["lockpick"] = "撬锁器",
        ["diamond"] = "钻石",
        ["gold"] = "金条",
        ["money"] = "现金",
        ["car_key"] = "车钥匙",

        -- 武器类
        ["weapon_pistol"] = "手枪",
        ["weapon_knife"] = "小刀",
        ["weapon_rifle"] = "步枪",
        ["weapon_carbinerifle"] = "卡宾枪",
        ["weapon_smg"] = "冲锋枪",

        -- 工具类
        ["repairkit"] = "修理包",
        ["advancedrepairkit"] = "高级修理包",
        ["radio"] = "对讲机",
        ["binoculars"] = "望远镜",

        -- 食物饮料
        ["burger"] = "汉堡",
        ["cola"] = "可乐",
        ["coffee"] = "咖啡",
        ["sandwich"] = "三明治",
        ["chips"] = "薯条",
        ["yinyue"] = "音乐盒",
        ["stone"] = "石头"

        

    }
}

-- 权限配置
Config.Permissions = {
    admin = "admin",          -- 管理员权限
    drawLottery = "admin"     -- 开奖权限（改为admin，更通用的权限名称）
}

-- 彩票店职业配置
Config.LotteryJob = {
    enabled = true,       -- 是否启用职业系统，如果启用则账户余额不足时提示联系工作人员，如果不启用则账户余额不足玩家也能兑换到奖金
    name = "lottery_shop", -- 职业名称
    label = "彩票店",        -- 职业显示名称
    adminAccess = 3,      -- 访问账户管理和员工管理所需的最低职业等级，默认3级以上可访问
    grades = {
        {
            name = "employee",    -- 等级名称
            label = "彩票店员工",  -- 等级显示名称
            grade = 0             -- 等级序号
        },
		{
            name = "sales",       -- 等级名称，改为sales避免与第一个等级重名
            label = "彩票店销售",  -- 等级显示名称
            grade = 1             -- 等级序号
        },
        {
            name = "manager",
            label = "彩票店经理",
            grade = 2
        },
        {
            name = "boss",
            label = "彩票店老板",
            grade = 3,
            isBoss = true         -- 是否为老板职位
        }
    }
}

-- 通知配置
Config.Notifications = {
    jackpotAnnouncement = true,  -- 大奖公告
    minJackpotAmount = 100000,   -- 最小公告金额
    serverWide = true            -- 全服公告
}

-- 调试模式
Config.Debug = {
    enabled = true,            -- 是否启用调试模式
    showDrawTimeCheck = false   -- 是否显示开奖时间检查日志（用于测试通知功能）
}

-- 彩票店管理系统配置
Config.AdminNPC = {
    createPed = true, -- 是否创建管理员NPC
    createBlip = true, -- 是否创建地图标记
    model = "a_f_y_business_02", -- 管理员NPC模型
    coords = vector4(375.875, 327.896, 103.566, 75.118), -- 管理员位置和朝向
    scenario = "WORLD_HUMAN_CLIPBOARD", -- NPC动作
    blip = {
        enabled = true,
        sprite = 500,
        color = 46,
        scale = 0.8,
        name = "彩票店管理员"
    }
}



