-- 奖池和奖金管理模块

-- 获取框架对象
local ESX, QBCore = nil, nil
if Config.Framework == 'ESX' then
    ESX = exports['es_extended']:getSharedObject()
elseif Config.Framework == 'QB' then
    QBCore = exports['qb-core']:GetCoreObject()
end

-- 调试打印函数 - 只在调试模式开启时打印
local function DebugPrint(message, level)
    if Config.Debug and Config.Debug.enabled then
        level = level or "^7" -- 默认白色
        print(level .. "[彩票系统-调试] " .. message)
    end
end

-- 系统消息打印函数 - 总是打印重要的系统消息
local function SystemPrint(message, level)
    level = level or "^7" -- 默认白色
    print(level .. "[彩票系统] " .. message)
end

-- 引用GivePlayerItem函数（从scratch模块）
local function GivePlayerItem(source, itemName, count, metadata)
    if exports.scratch and exports.scratch.GivePlayerItem then
        return exports.scratch:GivePlayerItem(source, itemName, count, metadata)
    else
        SystemPrint("^1[彩票系统] ^7警告: GivePlayerItem函数不可用")
        return false
    end
end

-- 初始化奖池
function InitializePrizePools()
    SystemPrint('^3[彩票系统] ^7正在初始化奖池...')
    
    -- 检查奖池表是否已存在
    local tableExists = MySQL.Sync.fetchScalar("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'prize_pools'")
    
    if not tableExists or tableExists == 0 then
        SystemPrint('^1[彩票系统] ^7奖池表不存在，请确保数据库初始化正确运行')
        return
    end
    
    -- 检查现有奖池数据
    local existingPools = MySQL.Sync.fetchAll('SELECT * FROM prize_pools')
    local poolsMap = {}
    
    if existingPools then
        for _, pool in ipairs(existingPools) do
            poolsMap[pool.lottery_type] = pool
        end
    end
    
    for lotteryType, config in pairs(Config.PrizePools) do
        if poolsMap[lotteryType] then
            -- 不再重置为初始值，保留实际金额
            SystemPrint(string.format('^2[彩票系统] ^7%s奖池已存在，当前金额：%s%d', 
                lotteryType == 'double_ball' and '双色球' or '大乐透',
                Config.Currency.symbol,
                poolsMap[lotteryType].current_amount
            ))
        else
            -- 使用REPLACE INTO语法，避免重复键错误
            local success = MySQL.Sync.execute('REPLACE INTO prize_pools (lottery_type, current_amount, total_contribution, last_draw_amount, rollover_count) VALUES (?, ?, ?, ?, ?)', {
                lotteryType,
                config.initial,
                0,
                0,
                0
            })
            
            if success then
                SystemPrint(string.format('^2[彩票系统] ^7%s奖池已初始化，初始金额：%s%d', 
                    lotteryType == 'double_ball' and '双色球' or '大乐透',
                    Config.Currency.symbol,
                    config.initial
                ))
            else
                SystemPrint(string.format('^1[彩票系统] ^7%s奖池初始化失败', 
                    lotteryType == 'double_ball' and '双色球' or '大乐透'
                ))
            end
        end
    end
    
    -- 确认初始化后的奖池状态
    local finalPools = MySQL.Sync.fetchAll('SELECT * FROM prize_pools')
    if finalPools then
        for _, pool in ipairs(finalPools) do
            SystemPrint(string.format('^3[彩票系统] ^7奖池状态检查: %s 当前金额: %s%d', 
                pool.lottery_type == 'double_ball' and '双色球' or '大乐透',
                Config.Currency.symbol,
                pool.current_amount
            ))
        end
    end
    
    SystemPrint('^2[彩票系统] ^7奖池初始化完成')
end

-- 更新奖池
function UpdatePrizePool(lotteryType, ticketPrice)
    local config = Config.PrizePools[lotteryType]
    if not config then return end
    
    -- 使用资金流向配置
    local prizePoolContribution = math.floor(ticketPrice * Config.MoneyFlow.lottery.prizePool)
    
    -- 异步更新奖池，避免阻塞购买操作
    MySQL.Async.execute(
        'UPDATE prize_pools SET current_amount = current_amount + ?, total_contribution = total_contribution + ? WHERE lottery_type = ?',
        {prizePoolContribution, prizePoolContribution, lotteryType},
        function(rowsChanged)
            if rowsChanged > 0 then
                SystemPrint(string.format("^2[彩票系统] ^7奖池异步更新成功 - 类型: %s, 增加金额: %d", 
                    lotteryType, prizePoolContribution))
            else
                SystemPrint(string.format("^1[彩票系统] ^7奖池更新失败 - 类型: %s, 增加金额: %d", 
                    lotteryType, prizePoolContribution))
            end
        end
    )
end

-- 更新彩票店账户
function UpdateShopAccount(amount)
    if amount <= 0 then return end
    
    if Config.Framework == "ESX" then
        -- 使用esx_addonaccount API添加销售收入
        TriggerEvent('esx_addonaccount:getSharedAccount', 'society_lottery', function(societyAccount)
            if societyAccount then
                societyAccount.addMoney(amount)
                SystemPrint(string.format("^2[彩票系统] ^7彩票店账户异步更新成功 - 增加金额: %d", amount))
                
                -- 记录交易明细
                Citizen.CreateThread(function()
                    -- 在独立线程中记录交易，避免阻塞主流程
                    exports[GetCurrentResourceName()]:LogTransaction('sales', amount, '彩票/刮刮乐销售收入', nil, nil)
                end)
            else
                SystemPrint(string.format("^1[彩票系统] ^7彩票店账户更新失败 - 金额: %d", amount))
            end
        end)
    else -- Config.Framework == "QB"
        -- 使用lottery_shop_accounts表更新
        MySQL.Async.execute('UPDATE lottery_shop_accounts SET balance = balance + ?, total_income = total_income + ? WHERE id = 1', 
            {amount, amount}, 
            function(rowsChanged)
                if rowsChanged > 0 then
                    SystemPrint(string.format("^2[彩票系统] ^7彩票店账户(QB)异步更新成功 - 增加金额: %d", amount))
                    
                    -- 记录交易明细
                    Citizen.CreateThread(function()
                        -- 在独立线程中记录交易，避免阻塞主流程
                        exports[GetCurrentResourceName()]:LogTransaction('sales', amount, '彩票/刮刮乐销售收入', nil, nil)
                    end)
                else
                    SystemPrint(string.format("^1[彩票系统] ^7彩票店账户(QB)更新失败 - 金额: %d", amount))
                end
            end
        )
    end
end

-- 获取奖池金额
function GetPrizePoolAmount(lotteryType)
    local result = MySQL.Sync.fetchScalar('SELECT current_amount FROM prize_pools WHERE lottery_type = ?', {lotteryType})
    DebugPrint(string.format("^3[彩票系统] ^7获取奖池金额 - 类型: %s, 金额: %d", lotteryType, result or 0))
    return result or 0
end

-- 获取所有奖池信息
RegisterNetEvent('caipiaoc:getPrizePools')
AddEventHandler('caipiaoc:getPrizePools', function(cb)
    local source = source
    
    -- 添加调试信息
    DebugPrint('^3[彩票系统] ^7客户端请求奖池信息，玩家ID: ' .. source)
    
    -- 使用正确的异步回调模式
    MySQL.Async.fetchAll('SELECT * FROM prize_pools ORDER BY lottery_type', {}, function(pools)
        if pools and #pools > 0 then
            DebugPrint('^2[彩票系统] ^7成功获取奖池数据: ' .. #pools .. '条记录')
            for _, pool in ipairs(pools) do
                DebugPrint(string.format('^2[彩票系统] ^7奖池数据: %s = %s%d', 
                    pool.lottery_type, 
                    Config.Currency.symbol, 
                    pool.current_amount))
            end
        else
            DebugPrint('^1[彩票系统] ^7警告: 未找到奖池数据或数据为空')
            -- 返回空数组，不再创建默认值
            pools = {}
            DebugPrint('^3[彩票系统] ^7返回空奖池数据')
        end
        
        -- 发送数据到客户端
        TriggerClientEvent('caipiaoc:receivePrizePools', source, pools)
    end)
end)

-- 兑奖
RegisterNetEvent('caipiaoc:claimPrize')
AddEventHandler('caipiaoc:claimPrize', function(data)
    local source = source
    local ticketId = data.ticketId
    local lotteryType = data.lotteryType
    
    SystemPrint("^3[彩票系统] ^7收到兑奖请求 - 票ID: " .. tostring(ticketId) .. ", 类型: " .. tostring(lotteryType))
    
    if not ticketId or not lotteryType then
        SystemPrint("^1[彩票系统] ^7兑奖失败: 参数错误")
        TriggerClientEvent('caipiaoc:claimPrizeResult', source, {success = false, message = "参数错误"})
        return
    end
    
    local playerIdentifier = GetPlayerIdentifier(source)
    if not playerIdentifier then
        SystemPrint("^1[彩票系统] ^7兑奖失败: 玩家数据错误")
        TriggerClientEvent('caipiaoc:claimPrizeResult', source, {success = false, message = "玩家数据错误"})
        return
    end
    
    local tableName = lotteryType == 'scratch' and 'scratch_cards' or 'lottery_tickets'
    local amountColumn = 'prize_amount'
    local actualLotteryType = lotteryType
    
    -- 如果是彩票，获取具体的彩票类型（double_ball或super_lotto）
    if lotteryType == 'lottery' then
        local ticketTypeResult = MySQL.Sync.fetchScalar('SELECT lottery_type FROM lottery_tickets WHERE id = ?', {ticketId})
        if ticketTypeResult then
            actualLotteryType = ticketTypeResult
            SystemPrint("^3[彩票系统] ^7彩票具体类型: " .. actualLotteryType)
        end
    end
    
    -- 使用同步查询替代异步查询
    local query = 'SELECT * FROM ' .. tableName .. ' WHERE id = ? AND player_id = ? AND ' .. amountColumn .. ' > 0 AND is_claimed = 0'
    SystemPrint("^3[彩票系统] ^7执行兑奖查询: " .. query)
    SystemPrint("^3[彩票系统] ^7参数: 票ID=" .. tostring(ticketId) .. ", 玩家ID=" .. playerIdentifier)
    
    local prize = MySQL.Sync.fetchAll(query, {ticketId, playerIdentifier})
    
    -- 检查查询结果
    if not prize or #prize == 0 then
        SystemPrint("^1[彩票系统] ^7兑奖失败: 找不到可兑奖的记录")
        
        -- 尝试查询该票是否存在但已兑奖
        local checkClaimed = MySQL.Sync.fetchAll('SELECT * FROM ' .. tableName .. ' WHERE id = ? AND player_id = ?', {ticketId, playerIdentifier})
        if checkClaimed and #checkClaimed > 0 then
            if checkClaimed[1].is_claimed == 1 then
                SystemPrint("^3[彩票系统] ^7该票已经兑奖过")
                TriggerClientEvent('caipiaoc:claimPrizeResult', source, {success = false, message = "该票已经兑奖过"})
            elseif checkClaimed[1][amountColumn] <= 0 then
                SystemPrint("^3[彩票系统] ^7该票没有中奖")
                TriggerClientEvent('caipiaoc:claimPrizeResult', source, {success = false, message = "该票没有中奖"})
            else
                SystemPrint("^1[彩票系统] ^7未知错误: 票存在但无法兑奖")
                TriggerClientEvent('caipiaoc:claimPrizeResult', source, {success = false, message = "未知错误"})
            end
        else
            TriggerClientEvent('caipiaoc:claimPrizeResult', source, {success = false, message = "找不到可兑奖的记录"})
        end
        return
    end
    
    -- 使用第一条记录
    local prizeRecord = prize[1]
    local prizeAmount = prizeRecord[amountColumn]
    
    SystemPrint("^2[彩票系统] ^7找到可兑奖记录: 票ID=" .. ticketId .. ", 奖金=" .. prizeAmount)
    
    -- 如果是刮刮乐，需要检查彩票店账户余额
    if lotteryType == 'scratch' then
        -- 计算手续费和实际支付金额
        local feeRate = 0  -- 手续费比例
        local feeAmount = 0  -- 手续费金额
        local feeMessage = ""  -- 手续费提示信息
        
        -- 根据奖金金额确定手续费比例
        if prizeAmount >= 10000 then
            -- 刮刮乐类型且金额大于等于一万：扣除10%的手续费
            feeRate = 0.1
            feeMessage = "刮刮乐大额兑奖(≥10,000)需扣除10%的手续费"
        end
        
        -- 计算手续费金额和实际支付金额
        feeAmount = math.floor(prizeAmount * feeRate)
        local actualPrizeAmount = prizeAmount - feeAmount
        
        if Config.Framework == "ESX" then
            -- 检查彩票店账户余额
            TriggerEvent('esx_addonaccount:getSharedAccount', 'society_lottery', function(societyAccount)
                -- 添加调试日志，输出账户状态和余额
                if societyAccount then
                    SystemPrint("^3[彩票系统-调试] ^7账户状态检查 - society_lottery账户余额: " .. societyAccount.money .. ", 需要支付: " .. actualPrizeAmount)
                else
                    SystemPrint("^1[彩票系统-调试] ^7严重错误: society_lottery账户对象为空!")
                end
                
                if societyAccount and societyAccount.money >= actualPrizeAmount then
                    -- 账户余额足够，尝试从账户扣除实际支付金额
                    -- 使用标记变量检查操作是否成功
                    local deductSuccess = true
                    
                    -- 尝试扣款
                    local previousMoney = societyAccount.money
                    societyAccount.removeMoney(actualPrizeAmount)
                    
                    -- 验证扣款是否成功（通过检查余额变化）
                    if societyAccount.money == (previousMoney - actualPrizeAmount) then
                        deductSuccess = true
                    end
                    
                    if deductSuccess then
                        -- 扣款成功，执行兑奖流程
                        ProcessClaimPrize(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, feeRate, feeAmount)
                    else
                        -- 扣款失败，检查是否启用职业系统
                        if Config.LotteryJob and Config.LotteryJob.enabled then
                            -- 职业系统启用，提示玩家联系彩票店工作人员
                            SendNotification(source, "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员", "error")
                        else
                            -- 职业系统未启用，尝试直接发放奖金
                            ProcessClaimPrize(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, feeRate, feeAmount)
                        end
                    end
                else
                    -- 账户余额不足，检查是否启用职业系统
                    if Config.LotteryJob and Config.LotteryJob.enabled then
                        -- 职业系统启用，提示玩家联系彩票店工作人员
                        SendNotification(source, "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员", "error")
                        
                        -- 返回失败信息给客户端
                        local message = "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员"
                        TriggerClientEvent('caipiaoc:claimPrizeResult', source, {success = false, message = message})
                    else
                        -- 职业系统未启用，尝试直接发放奖金
                        ProcessClaimPrize(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, feeRate, feeAmount)
                    end
                end
            end)
        else -- Config.Framework == "QB"
            -- 检查彩票店账户余额 (使用lottery_shop_accounts表)
            MySQL.Async.fetchAll('SELECT balance FROM lottery_shop_accounts WHERE id = 1', {}, function(result)
                -- 添加调试日志，输出账户状态和余额
                if result and #result > 0 then
                    SystemPrint("^3[彩票系统-调试] ^7账户状态检查 - lottery_shop_accounts账户余额: " .. result[1].balance .. ", 需要支付: " .. actualPrizeAmount)
                else
                    SystemPrint("^1[彩票系统-调试] ^7严重错误: lottery_shop_accounts表中无数据!")
                end
                
                if result and #result > 0 and result[1].balance >= actualPrizeAmount then
                    -- 账户余额足够，尝试从账户扣除实际支付金额
                    MySQL.Async.execute('UPDATE lottery_shop_accounts SET balance = balance - ?, total_payout = total_payout + ? WHERE id = 1 AND balance >= ?',
                        {actualPrizeAmount, actualPrizeAmount, actualPrizeAmount},
                        function(rowsChanged)
                            if rowsChanged > 0 then
                                -- 扣款成功，执行兑奖流程
                                ProcessClaimPrize(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, feeRate, feeAmount)
                            else
                                -- 扣款失败，检查是否启用职业系统
                                if Config.LotteryJob and Config.LotteryJob.enabled then
                                    -- 职业系统启用，提示玩家联系彩票店工作人员
                                    SendNotification(source, "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员", "error")
                                else
                                    -- 职业系统未启用，尝试直接发放奖金
                                    ProcessClaimPrize(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, feeRate, feeAmount)
                                end
                            end
                        end
                    )
                else
                    -- 账户余额不足，检查是否启用职业系统
                    if Config.LotteryJob and Config.LotteryJob.enabled then
                        -- 职业系统启用，提示玩家联系彩票店工作人员
                        SendNotification(source, "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员", "error")
                        
                        -- 返回失败信息给客户端
                        local message = "兑奖失败，彩票店账户余额不足，请联系彩票店工作人员"
                        TriggerClientEvent('caipiaoc:claimPrizeResult', source, {success = false, message = message})
                    else
                        -- 职业系统未启用，尝试直接发放奖金
                        ProcessClaimPrize(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, feeRate, feeAmount)
                    end
                end
            end)
        end
    else
        -- 处理大乐透/双色球兑奖
        -- 计算手续费和实际支付金额
        local feeRate = 0  -- 手续费比例
        local feeAmount = 0  -- 手续费金额
        local feeMessage = ""  -- 手续费提示信息
        
        -- 根据奖金金额确定手续费比例
        if prizeAmount >= 500000 then
            -- 大额彩票奖金：扣除20%的手续费
            feeRate = 0.2
            feeMessage = "大额彩票兑奖(≥500,000)需扣除20%的手续费"
        end
        
        -- 计算手续费金额和实际支付金额
        feeAmount = math.floor(prizeAmount * feeRate)
        local actualPrizeAmount = prizeAmount - feeAmount
        
        -- 直接处理兑奖，无需检查账户余额
        ProcessClaimPrize(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, feeRate, feeAmount)
    end
end)

-- 处理兑奖逻辑（抽取为函数以避免代码重复）
function ProcessClaimPrize(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, passedFeeRate, passedFeeAmount)
    -- 先标记为已兑奖，避免重复兑奖 (改为异步)
    MySQL.Async.execute('UPDATE ' .. tableName .. ' SET is_claimed = 1, claimed_time = NOW() WHERE id = ?', {ticketId}, function(rowsChanged)
        if not rowsChanged or rowsChanged <= 0 then
            SystemPrint("^1[彩票系统] ^7兑奖失败: 数据库更新失败")
            TriggerClientEvent('caipiaoc:claimPrizeResult', source, {success = false, message = "兑奖失败: 数据库错误"})
            return
        end

        -- 继续处理兑奖逻辑
        ProcessClaimPrizeLogic(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, passedFeeRate, passedFeeAmount)
    end)
end

-- 兑奖逻辑处理函数
function ProcessClaimPrizeLogic(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, passedFeeRate, passedFeeAmount)

    -- 清除中奖记录和兑奖记录缓存，确保数据实时更新
    exports[GetCurrentResourceName()]:ClearCache("winningRecords")
    exports[GetCurrentResourceName()]:ClearCache("claimRecords")
    exports[GetCurrentResourceName()]:ClearCache("unclaimedRecords")

    -- 立即清除未兑奖奖品缓存
    if Cache then
        Cache:Clear("unclaimedPrizes", "unclaimed_*")
        Cache:Clear("playerTickets", "*")
    end
    
    -- 计算手续费 (如果未传入则重新计算)
    local feeRate = passedFeeRate or 0 -- 使用传入的手续费率或默认为0
    local feeAmount = passedFeeAmount or 0 -- 使用传入的手续费金额或默认为0
    local feeMessage = "" -- 手续费提示信息
    
    -- 只在未传入手续费参数时计算
    if not passedFeeRate then
        -- 根据票据类型和奖金金额确定手续费比例
        if lotteryType == 'scratch' and prizeAmount >= 10000 then
            -- 刮刮乐类型且金额大于等于一万：扣除10%的手续费
            feeRate = 0.1
            feeMessage = "刮刮乐大额兑奖(≥10,000)需扣除10%的手续费"
        elseif lotteryType == 'lottery' and (actualLotteryType == 'double_ball' or actualLotteryType == 'super_lotto') and prizeAmount >= 500000 then
            -- 大乐透或双色球且奖金大于等于500000：扣除20%的手续费
            feeRate = 0.2
            feeMessage = "大额彩票兑奖(≥500,000)需扣除20%的手续费"
        end
        
        -- 计算手续费金额
        feeAmount = math.floor(prizeAmount * feeRate)
    end
    
    local actualPrizeAmount = prizeAmount - feeAmount

    -- 添加调试信息
    DebugPrint("^3[彩票系统] ^7ProcessClaimPrizeLogic调试 - 原始奖金: " .. prizeAmount .. ", 手续费: " .. feeAmount .. ", 实际支付: " .. actualPrizeAmount)

    -- 记录手续费信息
    if feeAmount > 0 then
        DebugPrint("^3[彩票系统] ^7兑奖手续费: 原始奖金=" .. prizeAmount .. ", 手续费率=" .. (feeRate * 100) .. "%, 手续费金额=" .. feeAmount .. ", 实际支付=" .. actualPrizeAmount)
    end

    -- 排列5特殊处理：发放物品奖励而不是现金
    if lotteryType == 'lottery' and actualLotteryType == 'arrange_five' then
        -- 处理排列5的物品奖励
        if prizeRecord.reward_data then
            local rewardData = json.decode(prizeRecord.reward_data)
            if rewardData and rewardData.item and rewardData.amount then
                -- 使用GivePlayerItem函数发放物品
                local success = GivePlayerItem(source, rewardData.item, rewardData.amount)

                if success then
                    DebugPrint("^2[彩票系统] ^7排列5兑奖成功: 票ID=" .. ticketId .. ", 物品=" .. rewardData.item .. " x" .. rewardData.amount)

                    -- 获取物品显示名称
                    local displayName = Config.Items.itemNames[rewardData.item] or rewardData.item
                    local rewardText = ""
                    if rewardData.item == "money" then
                        rewardText = displayName .. " ¥" .. rewardData.amount
                    else
                        rewardText = displayName .. " x" .. rewardData.amount
                    end

                    -- 通知玩家
                    TriggerClientEvent('caipiaoc:claimPrizeResult', source, {
                        success = true,
                        amount = 0, -- 排列5不发放现金
                        ticketId = ticketId,
                        originalAmount = 0,
                        feeAmount = 0,
                        feeMessage = "",
                        itemReward = rewardText
                    })

                    -- 记录交易明细
                    local adminName = GetPlayerName(source)
                    exports[GetCurrentResourceName()]:LogTransaction('prize', 0, '排列5兑奖-物品奖励: ' .. rewardText, playerIdentifier, adminName)

                    return
                else
                    DebugPrint("^1[彩票系统] ^7排列5兑奖失败: 无法给予物品 " .. rewardData.item .. " x" .. rewardData.amount)
                    TriggerClientEvent('caipiaoc:claimPrizeResult', source, {success = false, message = "物品发放失败"})

                    -- 恢复未兑奖状态
                    MySQL.Async.execute('UPDATE ' .. tableName .. ' SET is_claimed = 0, claimed_time = NULL WHERE id = ?', {ticketId})
                    return
                end
            else
                DebugPrint("^1[彩票系统] ^7排列5兑奖失败: 奖励数据格式错误")
                TriggerClientEvent('caipiaoc:claimPrizeResult', source, {success = false, message = "奖励数据错误"})

                -- 恢复未兑奖状态
                MySQL.Async.execute('UPDATE ' .. tableName .. ' SET is_claimed = 0, claimed_time = NULL WHERE id = ?', {ticketId})
                return
            end
        else
            DebugPrint("^1[彩票系统] ^7排列5兑奖失败: 未找到奖励数据")
            TriggerClientEvent('caipiaoc:claimPrizeResult', source, {success = false, message = "未找到奖励数据"})

            -- 恢复未兑奖状态
            MySQL.Async.execute('UPDATE ' .. tableName .. ' SET is_claimed = 0, claimed_time = NULL WHERE id = ?', {ticketId})
            return
        end
    end

    -- 给予实际奖金（非排列5彩票）
    if AddPlayerMoney(source, actualPrizeAmount) then
        DebugPrint("^2[彩票系统] ^7兑奖成功: 票ID=" .. ticketId .. ", 原始奖金=" .. prizeAmount .. ", 实际支付=" .. actualPrizeAmount)
        
        -- 记录交易明细 - 所有票据类型都记录
        local adminName = GetPlayerName(source)
        local ticketType = lotteryType == 'scratch' and '刮刮乐' or actualLotteryType
        exports[GetCurrentResourceName()]:LogTransaction('prize', -prizeAmount, ticketType .. '兑奖支出', playerIdentifier, adminName)
            
            -- 如果有手续费，记录手续费交易
            if feeAmount > 0 then
            exports[GetCurrentResourceName()]:LogTransaction('fee', feeAmount, ticketType .. '兑奖手续费', playerIdentifier, adminName)
        end
        
        -- 从奖池中扣除奖金（仅对彩票有效，刮刮乐不影响奖池）
        if lotteryType == 'lottery' and (actualLotteryType == 'double_ball' or actualLotteryType == 'super_lotto') then
            -- 获取彩票的奖项级别
            local prizeLevel = prizeRecord.prize_level or 0
            local config = actualLotteryType == 'double_ball' and Config.DoubleBall or Config.SuperLotto
            local prizeConfig = config.prizes[prizeLevel]

            -- 添加调试信息
            DebugPrint(string.format("^3[彩票系统] ^7奖池扣除调试 - 奖项级别: %d, 总奖金: %d, 彩票类型: %s",
                prizeLevel, prizeAmount, actualLotteryType))

            -- 计算需要从奖池中扣除的金额
            local deductAmount = prizeAmount

            -- 如果是一等奖或二等奖，只扣除奖池百分比部分
            if prizeLevel == 1 or prizeLevel == 2 then
                if prizeConfig and prizeConfig.poolPercent and prizeConfig.poolPercent > 0 then
                    -- 只扣除奖池百分比部分，不扣除固定奖金部分
                    deductAmount = prizeAmount - prizeConfig.amount
                    DebugPrint(string.format("^2[彩票系统] ^7%d级奖金拆分: 固定部分=%d, 奖池部分=%d",
                        prizeLevel, prizeConfig.amount, deductAmount))
                else
                    SystemPrint(string.format("^1[彩票系统] ^7警告: %d级奖项配置异常 - prizeConfig存在: %s, poolPercent: %s",
                        prizeLevel, tostring(prizeConfig ~= nil), tostring(prizeConfig and prizeConfig.poolPercent)))
                end
            else
                SystemPrint(string.format("^3[彩票系统] ^7%d级奖项为固定奖金，扣除全部奖金: %d",
                    prizeLevel, deductAmount))
            end
            
            -- 确保扣除金额不为负数
            if deductAmount > 0 then
                -- 使用同步执行确保更新成功
                MySQL.Sync.execute(
                    'UPDATE prize_pools SET current_amount = GREATEST(current_amount - ?, 0) WHERE lottery_type = ?',
                    {deductAmount, actualLotteryType}
                )
                DebugPrint(string.format("^2[彩票系统] ^7从%s奖池中扣除%d (总奖金=%d)",
                    actualLotteryType, deductAmount, prizeAmount))
            else
                SystemPrint(string.format("^2[彩票系统] ^7%s级奖金全部为固定部分，无需从奖池扣除", prizeLevel))
            end
            
            -- 广播更新后的奖池数据给所有客户端
            local pools = MySQL.Sync.fetchAll('SELECT * FROM prize_pools ORDER BY lottery_type')
                          TriggerClientEvent('caipiaoc:receivePrizePools', -1, pools or {})
          end
        
        -- 准备结果数据
        local resultData = {
            success = true, 
            amount = actualPrizeAmount, 
            ticketId = ticketId,
            originalAmount = prizeAmount,
            feeAmount = feeAmount,
            feeMessage = feeMessage
        }
        
        SystemPrint("^2[彩票系统] ^7发送兑奖成功结果: " .. json.encode(resultData))
        TriggerClientEvent('caipiaoc:claimPrizeResult', source, resultData)

        -- 立即通知客户端刷新未兑奖奖品列表
        Citizen.SetTimeout(50, function()
            -- 获取最新的未兑奖奖品数据并发送给客户端
            local playerIdentifier = GetPlayerIdentifier(source)
            if playerIdentifier then
                GetUnclaimedPrizes(playerIdentifier, function(prizes)
                    TriggerClientEvent('caipiaoc:receiveUnclaimedPrizes', source, prizes or {})
                end)
            end
        end)

        -- 再次延迟刷新，确保数据完全同步
        Citizen.SetTimeout(300, function()
            local playerIdentifier = GetPlayerIdentifier(source)
            if playerIdentifier then
                GetUnclaimedPrizes(playerIdentifier, function(prizes)
                    TriggerClientEvent('caipiaoc:receiveUnclaimedPrizes', source, prizes or {})
                end)
            end
        end)
    else
        -- 如果给予奖金失败，恢复未兑奖状态
        MySQL.Sync.execute('UPDATE ' .. tableName .. ' SET is_claimed = 0, claimed_time = NULL WHERE id = ?', {ticketId})
        
        SystemPrint("^1[彩票系统] ^7兑奖失败: 无法添加玩家金钱")
        local resultData = {success = false, message = "兑奖失败: 无法添加金钱"}
        SystemPrint("^1[彩票系统] ^7发送兑奖失败结果: " .. json.encode(resultData))
        TriggerClientEvent('caipiaoc:claimPrizeResult', source, resultData)
    end
end

-- 获取未兑奖奖品
RegisterNetEvent('caipiaoc:getUnclaimedPrizes')
AddEventHandler('caipiaoc:getUnclaimedPrizes', function()
    local source = source
    local playerIdentifier = GetPlayerIdentifier(source)
    
    DebugPrint("^3[彩票系统] ^7收到获取未兑奖奖品请求 - 玩家ID: " .. tostring(playerIdentifier))
    
    if not playerIdentifier then
        DebugPrint("^1[彩票系统] ^7获取未兑奖奖品失败: 玩家ID为空")
        TriggerClientEvent('caipiaoc:receiveUnclaimedPrizes', source, {})
        return
    end
    
    -- 使用异步版本的 GetUnclaimedPrizes
    GetUnclaimedPrizes(playerIdentifier, function(prizes)
        prizes = prizes or {}

        DebugPrint("^2[彩票系统] ^7获取未兑奖奖品成功 - 找到 " .. #prizes .. " 条记录")
        for i, prize in ipairs(prizes) do
            local prizeType = prize.card_type and "刮刮乐" or "彩票"
            local typeName = prize.card_type or prize.lottery_type
            DebugPrint(string.format("^2[彩票系统] ^7未兑奖#%d: %s, 类型=%s, ID=%d, 奖金=%d",
                i, prizeType, typeName, prize.id, prize.prize_amount))
        end

        TriggerClientEvent('caipiaoc:receiveUnclaimedPrizes', source, prizes)
    end)
end)

-- 奖池管理命令 (管理员)
RegisterCommand('lottery_pool', function(source, args, rawCommand)
    if source == 0 or HasPermission(source, Config.Permissions.admin) then
        local action = args[1]
        local lotteryType = args[2]
        local amount = tonumber(args[3])
        
        if action == 'add' and lotteryType and amount then
            if lotteryType ~= 'double_ball' and lotteryType ~= 'super_lotto' then
                if source > 0 then
                    SendNotification(source, "无效的彩票类型", "error")
                else
                    SystemPrint("无效的彩票类型")
                end
                return
            end
            
            MySQL.Async.execute(
                'UPDATE prize_pools SET current_amount = current_amount + ? WHERE lottery_type = ?',
                {amount, lotteryType}
            )
            
            local message = string.format("已向%s奖池添加%s%d", 
                lotteryType == 'double_ball' and '双色球' or '大乐透',
                Config.Currency.symbol,
                amount
            )
            
            if source > 0 then
                SendNotification(source, message, "success")
            else
                SystemPrint(message)
            end
            
        elseif action == 'set' and lotteryType and amount then
            if lotteryType ~= 'double_ball' and lotteryType ~= 'super_lotto' then
                if source > 0 then
                    SendNotification(source, "无效的彩票类型", "error")
                else
                    SystemPrint("无效的彩票类型")
                end
                return
            end
            
            MySQL.Async.execute(
                'UPDATE prize_pools SET current_amount = ? WHERE lottery_type = ?',
                {amount, lotteryType}
            )
            
            local message = string.format("已设置%s奖池为%s%d", 
                lotteryType == 'double_ball' and '双色球' or '大乐透',
                Config.Currency.symbol,
                amount
            )
            
            if source > 0 then
                SendNotification(source, message, "success")
            else
                SystemPrint(message)
            end
            
        elseif action == 'check' then
            local pools = MySQL.Async.fetchAll('SELECT * FROM prize_pools')
            
            if source > 0 then
                for _, pool in pairs(pools or {}) do
                    local typeName = pool.lottery_type == 'double_ball' and '双色球' or '大乐透'
                    SendNotification(source, string.format("%s奖池：%s%d", typeName, Config.Currency.symbol, pool.current_amount), "info")
                end
            else
                for _, pool in pairs(pools or {}) do
                    local typeName = pool.lottery_type == 'double_ball' and '双色球' or '大乐透'
                    SystemPrint(string.format("%s奖池：%s%d", typeName, Config.Currency.symbol, pool.current_amount))
                end
            end
        else
            local usage = "用法: /lottery_pool [add|set|check] [double_ball|super_lotto] [金额]"
            if source > 0 then
                SendNotification(source, usage, "info")
            else
                SystemPrint(usage)
            end
        end
    else
        SendNotification(source, "权限不足", "error")
    end
end, false)

-- 重置奖池 (开奖后调用)
function ResetPrizePool(lotteryType, winnersCount)
    local config = Config.PrizePools[lotteryType]
    if not config then return end
    
    local currentPool = GetPrizePoolAmount(lotteryType)
    
    if winnersCount > 0 then
        -- 有人中奖，重置为初始值
        MySQL.Async.execute(
            'UPDATE prize_pools SET current_amount = ?, last_draw_amount = ?, rollover_count = 0 WHERE lottery_type = ?',
            {config.initial, currentPool, lotteryType}
        )
    else
        -- 无人中奖，滚存
        local rolloverCount = MySQL.Async.fetchScalar('SELECT rollover_count FROM prize_pools WHERE lottery_type = ?', {lotteryType}) or 0
        
        if rolloverCount < config.maxRollover then
            MySQL.Async.execute(
                'UPDATE prize_pools SET rollover_count = rollover_count + 1, last_draw_amount = ? WHERE lottery_type = ?',
                {currentPool, lotteryType}
            )
        else
            -- 达到最大滚存，强制重置
            MySQL.Async.execute(
                'UPDATE prize_pools SET current_amount = ?, rollover_count = 0, last_draw_amount = ? WHERE lottery_type = ?',
                {config.initial, currentPool, lotteryType}
            )
        end
    end
end

-- 获取奖金分配详情
function GetPrizeDistribution(lotteryType, winners)
    local config = lotteryType == 'double_ball' and Config.DoubleBall or Config.SuperLotto
    local distribution = {}
    
    -- 按奖项级别统计中奖人数
    local levelCounts = {}
    for _, winner in pairs(winners) do
        local level = winner.prizeLevel
        levelCounts[level] = (levelCounts[level] or 0) + 1
    end
    
    -- 计算各奖项奖金
    for level, count in pairs(levelCounts) do
        local prizeInfo = config.prizes[level]
        if prizeInfo then
            table.insert(distribution, {
                level = level,
                name = prizeInfo.name,
                count = count,
                singleAmount = prizeInfo.amount,
                totalAmount = prizeInfo.amount * count
            })
        end
    end
    
    return distribution
end

-- 定期更新奖池 (每小时执行一次)
CreateThread(function()
    while true do
        Wait(3600000) -- 1小时
        
        -- 这里可以添加奖池的自动增长逻辑
        -- 例如每小时自动增加一定金额
        
        if Config.Debug and Config.Debug.enabled then
            print('^3[彩票系统-调试] ^7执行奖池定期更新')
        end
    end
end)

-- 获取历史奖池数据 (用于统计)
function GetPrizePoolHistory(lotteryType, days)
    days = days or 30
    return MySQL.Async.fetchAll(
        'SELECT draw_date, jackpot_amount FROM draw_history WHERE lottery_type = ? AND draw_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY) ORDER BY draw_date DESC',
        {lotteryType, days}
    )
end

-- 重置所有奖池命令 (管理员)
RegisterCommand('reset_prize_pools', function(source, args, rawCommand)
    if source == 0 or HasPermission(source, Config.Permissions.admin) then
        SystemPrint('^3[彩票系统] ^7正在重置所有奖池...')
        
        for lotteryType, config in pairs(Config.PrizePools) do
            MySQL.Async.execute(
                'UPDATE prize_pools SET current_amount = ?, total_contribution = 0, rollover_count = 0 WHERE lottery_type = ?',
                {config.initial, lotteryType}
            )
            
            local message = string.format("%s奖池已重置为初始值%s%d", 
                lotteryType == 'double_ball' and '双色球' or '大乐透',
                Config.Currency.symbol,
                config.initial
            )
            
            if source > 0 then
                SendNotification(source, message, "success")
            else
                SystemPrint(message)
            end
        end
        
        -- 广播更新给所有客户端
        local pools = MySQL.Sync.fetchAll('SELECT * FROM prize_pools')
        TriggerClientEvent('caipiaoc:receivePrizePools', -1, pools or {})
        
        if source > 0 then
            SendNotification(source, "所有奖池已重置完成", "success")
        else
            SystemPrint("^2[彩票系统] ^7所有奖池已重置完成")
        end
    else
        SendNotification(source, "权限不足", "error")
    end
end, false)

-- 添加调试命令，可以手动触发奖池更新
RegisterCommand('update_prize_pools', function(source, args, rawCommand)
    if source == 0 or HasPermission(source, Config.Permissions.admin) then
        SystemPrint('^3[彩票系统] ^7手动更新奖池信息...')
        
        -- 获取最新奖池数据
        local pools = MySQL.Sync.fetchAll('SELECT * FROM prize_pools ORDER BY lottery_type')
        
        -- 显示奖池数据
        if pools and #pools > 0 then
            for _, pool in ipairs(pools) do
                SystemPrint(string.format('^2[彩票系统] ^7奖池数据: %s = %s%d', 
                    pool.lottery_type, 
                    Config.Currency.symbol, 
                    pool.current_amount))
            end
            
            -- 广播给所有客户端
            SystemPrint('^2[彩票系统] ^7广播奖池数据给所有客户端')
            TriggerClientEvent('caipiaoc:receivePrizePools', -1, pools)
            
            -- 如果是玩家执行命令，发送通知
            if source > 0 then
                SendNotification(source, "奖池数据已更新并广播", "success")
            end
        else
            SystemPrint('^1[彩票系统] ^7警告: 未找到奖池数据')
            
            if source > 0 then
                SendNotification(source, "未找到奖池数据", "error")
            end
        end
    else
        SendNotification(source, "权限不足", "error")
    end
end, false)

-- 导出函数
exports('GetPrizePoolAmount', GetPrizePoolAmount)
exports('UpdatePrizePool', UpdatePrizePool)
exports('UpdateShopAccount', UpdateShopAccount)
exports('ResetPrizePool', ResetPrizePool)

-- 处理管理系统兑奖逻辑（针对管理员兑奖）
function ProcessAdminClaimPrize(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, passedFeeRate, passedFeeAmount)
    -- 先标记为已兑奖，避免重复兑奖 (改为异步)
    MySQL.Async.execute('UPDATE ' .. tableName .. ' SET is_claimed = 1, claimed_time = NOW() WHERE id = ?', {ticketId}, function(rowsChanged)
        if not rowsChanged or rowsChanged <= 0 then
            SystemPrint("^1[彩票系统] ^7兑奖失败: 数据库更新失败")
            TriggerClientEvent('lottery:notification', source, "兑奖失败", "数据库更新失败", "error")
            return
        end

        -- 继续处理管理员兑奖逻辑
        ProcessAdminClaimPrizeLogic(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, passedFeeRate, passedFeeAmount)
    end)
end

-- 管理员兑奖逻辑处理函数
function ProcessAdminClaimPrizeLogic(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, passedFeeRate, passedFeeAmount)

    -- 清除中奖记录和兑奖记录缓存，确保数据实时更新
    exports[GetCurrentResourceName()]:ClearCache("winningRecords")
    exports[GetCurrentResourceName()]:ClearCache("claimRecords")
    exports[GetCurrentResourceName()]:ClearCache("unclaimedRecords")

    -- 立即清除未兑奖奖品缓存
    if Cache then
        Cache:Clear("unclaimedPrizes", "unclaimed_*")
        Cache:Clear("playerTickets", "*")
    end
    
    -- 计算手续费 (如果未传入则重新计算)
    local feeRate = passedFeeRate or 0 -- 使用传入的手续费率或默认为0
    local feeAmount = passedFeeAmount or 0 -- 使用传入的手续费金额或默认为0
    local feeMessage = "" -- 手续费提示信息
    
    -- 只在未传入手续费参数时计算
    if not passedFeeRate then
        -- 根据票据类型和奖金金额确定手续费比例
        if lotteryType == 'scratch' and prizeAmount >= 10000 then
            -- 刮刮乐类型且金额大于等于一万：扣除10%的手续费
            feeRate = 0.1
            feeMessage = "刮刮乐大额兑奖(≥10,000)需扣除10%的手续费"
        elseif lotteryType == 'lottery' and (actualLotteryType == 'double_ball' or actualLotteryType == 'super_lotto') and prizeAmount >= 500000 then
            -- 大乐透或双色球且奖金大于等于500000：扣除20%的手续费
            feeRate = 0.2
            feeMessage = "大额彩票兑奖(≥500,000)需扣除20%的手续费"
        end
        
        -- 计算手续费金额
        feeAmount = math.floor(prizeAmount * feeRate)
    end
    
    local actualPrizeAmount = prizeAmount - feeAmount

    -- 添加调试信息
    DebugPrint("^3[彩票系统] ^7ProcessAdminClaimPrizeLogic调试 - 原始奖金: " .. prizeAmount .. ", 手续费: " .. feeAmount .. ", 实际支付: " .. actualPrizeAmount)

    -- 记录手续费信息
    if feeAmount > 0 then
        DebugPrint("^3[彩票系统] ^7兑奖手续费: 原始奖金=" .. prizeAmount .. ", 手续费率=" .. (feeRate * 100) .. "%, 手续费金额=" .. feeAmount .. ", 实际支付=" .. actualPrizeAmount)
    end

    -- 获取中奖者ID
    local winnerIdentifier = prizeRecord.player_id
    local winnerName = prizeRecord.player_name

    DebugPrint("^2[彩票系统] ^7准备向中奖者发放奖金: ID=" .. winnerIdentifier .. ", 名称=" .. winnerName)
    
    -- 获取中奖者在线状态
    local winnerSource = GetPlayerSourceByIdentifier(winnerIdentifier)
    
    -- 如果中奖者在线，直接发放奖金
    if winnerSource then
        -- 排列5特殊处理：发放物品奖励而不是现金
        if lotteryType == 'lottery' and actualLotteryType == 'arrange_five' then
            -- 处理排列5的物品奖励
            if prizeRecord.reward_data then
                local rewardData = json.decode(prizeRecord.reward_data)
                if rewardData and rewardData.item and rewardData.amount then
                    -- 使用GivePlayerItem函数发放物品
                    local success = GivePlayerItem(winnerSource, rewardData.item, rewardData.amount)

                    if success then
                        DebugPrint("^2[彩票系统] ^7排列5管理员兑奖成功: 票ID=" .. ticketId .. ", 物品=" .. rewardData.item .. " x" .. rewardData.amount)

                        -- 获取物品显示名称
                        local displayName = Config.Items.itemNames[rewardData.item] or rewardData.item
                        local rewardText = ""
                        if rewardData.item == "money" then
                            rewardText = displayName .. " ¥" .. rewardData.amount
                        else
                            rewardText = displayName .. " x" .. rewardData.amount
                        end

                        -- 通知中奖者
                        TriggerClientEvent('lottery:notification', winnerSource, "恭喜中奖", "您的排列5彩票已被兑奖，获得奖品：" .. rewardText, "success")

                        -- 通知管理员
                        TriggerClientEvent('lottery:notification', source, "兑奖成功", "玩家 " .. winnerName .. " 的排列5彩票已兑奖，奖品：" .. rewardText, "success")

                        -- 记录交易明细
                        local adminName = GetPlayerName(source)
                        exports[GetCurrentResourceName()]:LogTransaction('prize', 0, '排列5兑奖-物品奖励: ' .. rewardText, winnerIdentifier, adminName)

                        return true
                    else
                        DebugPrint("^1[彩票系统] ^7排列5管理员兑奖失败: 无法给予物品 " .. rewardData.item .. " x" .. rewardData.amount)
                        TriggerClientEvent('lottery:notification', source, "兑奖失败", "无法给玩家 " .. winnerName .. " 发放物品", "error")

                        -- 恢复未兑奖状态
                        MySQL.Sync.execute('UPDATE ' .. tableName .. ' SET is_claimed = 0, claimed_time = NULL WHERE id = ?', {ticketId})
                        return false
                    end
                else
                    DebugPrint("^1[彩票系统] ^7排列5管理员兑奖失败: 奖励数据格式错误")
                    TriggerClientEvent('lottery:notification', source, "兑奖失败", "奖励数据格式错误", "error")

                    -- 恢复未兑奖状态
                    MySQL.Sync.execute('UPDATE ' .. tableName .. ' SET is_claimed = 0, claimed_time = NULL WHERE id = ?', {ticketId})
                    return false
                end
            else
                DebugPrint("^1[彩票系统] ^7排列5管理员兑奖失败: 未找到奖励数据")
                TriggerClientEvent('lottery:notification', source, "兑奖失败", "未找到奖励数据", "error")

                -- 恢复未兑奖状态
                MySQL.Sync.execute('UPDATE ' .. tableName .. ' SET is_claimed = 0, claimed_time = NULL WHERE id = ?', {ticketId})
                return false
            end
        end

        -- 非排列5彩票的正常现金发放
        if AddPlayerMoney(winnerSource, actualPrizeAmount) then
            DebugPrint("^2[彩票系统] ^7兑奖成功: 票ID=" .. ticketId .. ", 发放给在线中奖者: " .. winnerName .. ", 奖金=" .. actualPrizeAmount)
            
            -- 通知中奖者
            TriggerClientEvent('lottery:notification', winnerSource, "恭喜中奖", "您的彩票已被兑奖，获得奖金：¥" .. actualPrizeAmount, "success")
            
            -- 记录交易明细 - 修改记录格式，显示操作人为执行兑奖的管理员
            local adminName = GetPlayerName(source)
            local ticketType = lotteryType == 'scratch' and '刮刮乐' or actualLotteryType
            
            -- 刮刮乐记录实际金额，非刮刮乐(大乐透/双色球/秒速)记录为0元，并说明奖池扣除
            local transactionAmount = (lotteryType == 'scratch') and -prizeAmount or 0
            local transactionDesc = (lotteryType == 'scratch') 
                and (ticketType .. '兑奖支出') 
                or (ticketType .. '兑奖(奖池扣除' .. prizeAmount .. '元)')
            
            exports[GetCurrentResourceName()]:LogTransaction('prize', transactionAmount, transactionDesc, winnerIdentifier, adminName)
            
            -- 如果有手续费，记录手续费交易
            if feeAmount > 0 then
                exports[GetCurrentResourceName()]:LogTransaction('fee', feeAmount, ticketType .. '兑奖手续费', winnerIdentifier, adminName)
                
                -- 注释掉这部分代码，因为手续费已经在调用此函数之前增加到账户余额了
                -- if (lotteryType == 'lottery' and (actualLotteryType == 'double_ball' or actualLotteryType == 'super_lotto')) or lotteryType == 'scratch' then
                --     -- 增加手续费到彩票店账户
                --     MySQL.Sync.execute('UPDATE lottery_shop_accounts SET balance = balance + ? WHERE id = 1', {feeAmount})
                --     SystemPrint("^2[彩票系统] ^7兑奖手续费已增加到账户余额: 类型=" .. ticketType .. ", 金额=" .. feeAmount)
                -- end
            end
            
            -- 通知管理员
            TriggerClientEvent('lottery:notification', source, "兑奖成功", "已成功向玩家 " .. winnerName .. " 发放奖金: ¥" .. actualPrizeAmount, "success")
            
            -- 从奖池中扣除奖金（仅对彩票有效，刮刮乐不影响奖池）
            if lotteryType == 'lottery' and (actualLotteryType == 'double_ball' or actualLotteryType == 'super_lotto') then
                UpdateLotteryPrizePool(prizeRecord, prizeAmount, actualLotteryType)
            end
            
            -- 刷新管理系统数据
            TriggerClientEvent('lottery:refreshAdminData', -1)

            -- 立即刷新中奖者的未兑奖奖品列表
            if winnerSource then
                Citizen.SetTimeout(50, function()
                    local winnerIdentifier = GetPlayerIdentifier(winnerSource)
                    if winnerIdentifier then
                        GetUnclaimedPrizes(winnerIdentifier, function(prizes)
                            TriggerClientEvent('caipiaoc:receiveUnclaimedPrizes', winnerSource, prizes or {})
                        end)
                    end
                end)

                -- 再次延迟刷新，确保数据完全同步
                Citizen.SetTimeout(300, function()
                    local winnerIdentifier = GetPlayerIdentifier(winnerSource)
                    if winnerIdentifier then
                        GetUnclaimedPrizes(winnerIdentifier, function(prizes)
                            TriggerClientEvent('caipiaoc:receiveUnclaimedPrizes', winnerSource, prizes or {})
                        end)
                    end
                end)
            end

            return true
        else
            -- 发放奖金失败，恢复未兑奖状态
            MySQL.Sync.execute('UPDATE ' .. tableName .. ' SET is_claimed = 0, claimed_time = NULL WHERE id = ?', {ticketId})
            
            SystemPrint("^1[彩票系统] ^7兑奖失败: 无法添加玩家金钱")
            TriggerClientEvent('lottery:notification', source, "兑奖失败", "无法给玩家添加金钱", "error")
            return false
        end
    else
        -- 中奖者不在线，使用离线奖金发放
        SystemPrint("^3[彩票系统] ^7中奖者不在线，将奖金记录到离线表中")

        -- 排列5特殊处理：记录物品奖励信息
        if lotteryType == 'lottery' and actualLotteryType == 'arrange_five' then
            if prizeRecord.reward_data then
                local rewardData = json.decode(prizeRecord.reward_data)
                if rewardData and rewardData.item and rewardData.amount then
                    -- 获取物品显示名称
                    local displayName = Config.Items.itemNames[rewardData.item] or rewardData.item
                    local rewardText = ""
                    if rewardData.item == "money" then
                        rewardText = displayName .. " ¥" .. rewardData.amount
                    else
                        rewardText = displayName .. " x" .. rewardData.amount
                    end

                    -- 记录到离线奖金表，使用特殊格式标识物品奖励
                    MySQL.Async.execute('INSERT INTO lottery_offline_prizes (player_id, player_name, prize_amount, prize_type, ticket_id) VALUES (?, ?, ?, ?, ?)',
                        {winnerIdentifier, winnerName, 0, '排列5-物品奖励:' .. prizeRecord.reward_data, ticketId},
                        function(insertResult)
                            if insertResult then
                                SystemPrint("^2[彩票系统] ^7已将排列5物品奖励记录到离线表: 玩家=" .. winnerName .. ", 奖品=" .. rewardText)

                                -- 记录交易明细
                                local adminName = GetPlayerName(source)
                                exports[GetCurrentResourceName()]:LogTransaction('prize', 0, '排列5兑奖-物品奖励(离线): ' .. rewardText, winnerIdentifier, adminName)

                                -- 通知管理员
                                TriggerClientEvent('lottery:notification', source, "兑奖成功", "玩家 " .. winnerName .. " 不在线，排列5奖品 " .. rewardText .. " 已记录，他下次上线时将自动领取", "success")

                                return true
                            else
                                -- 记录失败，恢复未兑奖状态
                                MySQL.Sync.execute('UPDATE ' .. tableName .. ' SET is_claimed = 0, claimed_time = NULL WHERE id = ?', {ticketId})

                                SystemPrint("^1[彩票系统] ^7排列5兑奖失败: 无法记录离线物品奖励")
                                TriggerClientEvent('lottery:notification', source, "兑奖失败", "无法记录离线物品奖励", "error")
                                return false
                            end
                        end
                    )
                    return true
                else
                    DebugPrint("^1[彩票系统] ^7排列5离线兑奖失败: 奖励数据格式错误")
                    TriggerClientEvent('lottery:notification', source, "兑奖失败", "奖励数据格式错误", "error")

                    -- 恢复未兑奖状态
                    MySQL.Sync.execute('UPDATE ' .. tableName .. ' SET is_claimed = 0, claimed_time = NULL WHERE id = ?', {ticketId})
                    return false
                end
            else
                DebugPrint("^1[彩票系统] ^7排列5离线兑奖失败: 未找到奖励数据")
                TriggerClientEvent('lottery:notification', source, "兑奖失败", "未找到奖励数据", "error")

                -- 恢复未兑奖状态
                MySQL.Sync.execute('UPDATE ' .. tableName .. ' SET is_claimed = 0, claimed_time = NULL WHERE id = ?', {ticketId})
                return false
            end
        end

        -- 检查离线奖金表是否存在（表结构已在database.lua中统一管理）
        -- 这里不再重复创建表，直接插入数据

        -- 记录到离线奖金表（非排列5彩票）
        local prizeType = lotteryType == 'scratch' and '刮刮乐' or actualLotteryType
        MySQL.Async.execute('INSERT INTO lottery_offline_prizes (player_id, player_name, prize_amount, prize_type, ticket_id) VALUES (?, ?, ?, ?, ?)',
            {winnerIdentifier, winnerName, actualPrizeAmount, prizeType, ticketId},
            function(insertResult)
                if insertResult then
                    SystemPrint("^2[彩票系统] ^7已将奖金记录到离线表: 玩家=" .. winnerName .. ", 奖金=" .. actualPrizeAmount)

                    -- 记录交易明细 - 修改记录格式，显示操作人为执行兑奖的管理员
                    local adminName = GetPlayerName(source)

                    -- 刮刮乐记录实际金额，非刮刮乐(大乐透/双色球/秒速)记录为0元，并说明奖池扣除
                    local transactionAmount = (lotteryType == 'scratch') and -prizeAmount or 0
                    local transactionDesc = (lotteryType == 'scratch')
                        and (prizeType .. '兑奖支出(离线)')
                        or (prizeType .. '兑奖(奖池扣除' .. prizeAmount .. '元)(离线)')

                    exports[GetCurrentResourceName()]:LogTransaction('prize', transactionAmount, transactionDesc, winnerIdentifier, adminName)

                    -- 如果有手续费，记录手续费交易
                    if feeAmount > 0 then
                        exports[GetCurrentResourceName()]:LogTransaction('fee', feeAmount, prizeType .. '兑奖手续费(离线)', winnerIdentifier, adminName)

                        -- 注释掉这部分代码，因为手续费已经在调用此函数之前增加到账户余额了
                        -- if (lotteryType == 'lottery' and (actualLotteryType == 'double_ball' or actualLotteryType == 'super_lotto')) or lotteryType == 'scratch' then
                        --     -- 增加手续费到彩票店账户
                        --     MySQL.Sync.execute('UPDATE lottery_shop_accounts SET balance = balance + ? WHERE id = 1', {feeAmount})
                        --     SystemPrint("^2[彩票系统] ^7兑奖手续费已增加到账户余额: 类型=" .. prizeType .. ", 金额=" .. feeAmount)
                        -- end
                    end

                    -- 通知管理员
                    TriggerClientEvent('lottery:notification', source, "兑奖成功", "玩家 " .. winnerName .. " 不在线，奖金 ¥" .. actualPrizeAmount .. " 已记录，他下次上线时将自动领取", "success")

                    -- 从奖池中扣除奖金（仅对彩票有效，刮刮乐不影响奖池）
                    if lotteryType == 'lottery' and (actualLotteryType == 'double_ball' or actualLotteryType == 'super_lotto') then
                        UpdateLotteryPrizePool(prizeRecord, prizeAmount, actualLotteryType)
                    end

                    return true
                else
                    -- 记录失败，恢复未兑奖状态
                    MySQL.Sync.execute('UPDATE ' .. tableName .. ' SET is_claimed = 0, claimed_time = NULL WHERE id = ?', {ticketId})

                    SystemPrint("^1[彩票系统] ^7兑奖失败: 无法记录离线奖金")
                    TriggerClientEvent('lottery:notification', source, "兑奖失败", "无法记录离线奖金", "error")
                    return false
                end
            end
        )
    end
    
    return true
end

-- 更新彩票奖池（从奖池中扣除奖金）
function UpdateLotteryPrizePool(prizeRecord, prizeAmount, actualLotteryType)
    -- 获取彩票的奖项级别
    local prizeLevel = prizeRecord.prize_level or 0
    local config = actualLotteryType == 'double_ball' and Config.DoubleBall or Config.SuperLotto
    local prizeConfig = config.prizes[prizeLevel]

    -- 添加调试信息
    DebugPrint(string.format("^3[彩票系统] ^7UpdateLotteryPrizePool调试 - 奖项级别: %d, 总奖金: %d, 彩票类型: %s",
        prizeLevel, prizeAmount, actualLotteryType))

    -- 计算需要从奖池中扣除的金额
    local deductAmount = prizeAmount

    -- 如果是一等奖或二等奖，只扣除奖池百分比部分
    if prizeLevel == 1 or prizeLevel == 2 then
        if prizeConfig and prizeConfig.poolPercent and prizeConfig.poolPercent > 0 then
            -- 只扣除奖池百分比部分，不扣除固定奖金部分
            deductAmount = prizeAmount - prizeConfig.amount
            DebugPrint(string.format("^2[彩票系统] ^7%d级奖金拆分: 固定部分=%d, 奖池部分=%d",
                prizeLevel, prizeConfig.amount, deductAmount))
        else
            SystemPrint(string.format("^1[彩票系统] ^7警告: %d级奖项配置异常 - prizeConfig存在: %s, poolPercent: %s",
                prizeLevel, tostring(prizeConfig ~= nil), tostring(prizeConfig and prizeConfig.poolPercent)))
        end
    else
        SystemPrint(string.format("^3[彩票系统] ^7%d级奖项为固定奖金，扣除全部奖金: %d",
            prizeLevel, deductAmount))
    end
    
    -- 确保扣除金额不为负数
    if deductAmount > 0 then
        -- 使用同步执行确保更新成功
        MySQL.Sync.execute(
            'UPDATE prize_pools SET current_amount = GREATEST(current_amount - ?, 0) WHERE lottery_type = ?',
            {deductAmount, actualLotteryType}
        )
        DebugPrint(string.format("^2[彩票系统] ^7从%s奖池中扣除%d (总奖金=%d)",
            actualLotteryType, deductAmount, prizeAmount))
    else
        SystemPrint(string.format("^2[彩票系统] ^7%s级奖金全部为固定部分，无需从奖池扣除", prizeLevel))
    end
    
    -- 广播更新后的奖池数据给所有客户端
    local pools = MySQL.Sync.fetchAll('SELECT * FROM prize_pools ORDER BY lottery_type')
    TriggerClientEvent('caipiaoc:receivePrizePools', -1, pools or {})
end

-- 修改管理系统兑奖请求处理，使用新的ProcessAdminClaimPrize函数
RegisterNetEvent('caipiaoc:adminClaimPrize')
AddEventHandler('caipiaoc:adminClaimPrize', function(data)
    local source = source
    local ticketId = data.ticketId
    local lotteryType = data.lotteryType
    
    DebugPrint("^3[彩票系统] ^7收到管理系统兑奖请求 - 票ID: " .. tostring(ticketId) .. ", 类型: " .. tostring(lotteryType))
    
    if not ticketId or not lotteryType then
        SystemPrint("^1[彩票系统] ^7兑奖失败: 参数错误")
        TriggerClientEvent('lottery:notification', source, "兑奖失败", "参数错误", "error")
        return
    end
    
    -- 获取玩家标识符
    local playerIdentifier = GetPlayerIdentifier(source)
    if not playerIdentifier then
        SystemPrint("^1[彩票系统] ^7兑奖失败: 无法获取玩家标识符")
        TriggerClientEvent('lottery:notification', source, "兑奖失败", "无法获取玩家标识符", "error")
        return
    end
    
    -- 检查员工信息和手续费分成设置
    MySQL.Async.fetchAll('SELECT * FROM lottery_employees WHERE employee_id = ?', {playerIdentifier}, function(employeeResult)
        local commissionSettings = {
            enabled = false,
            rate = 0.1,
            minFeeAmount = 500
        }
        
        -- 如果有记录，使用数据库中的设置
        if employeeResult and #employeeResult > 0 then
            -- 打印完整的员工数据进行调试
            DebugPrint("^2[彩票系统] ^7查询到员工数据: ID=" .. playerIdentifier)

            -- 检查数据类型和值
            local enabledValue = employeeResult[1].fee_commission_enabled
            DebugPrint("^2[彩票系统] ^7数据库中的enabled原始值=" .. tostring(enabledValue) .. ", 类型=" .. type(enabledValue))
            
            -- 确保正确解析布尔值 (MySQL值可能是数字、字符串或布尔值)
            commissionSettings.enabled = (enabledValue == 1 or enabledValue == true or enabledValue == "1" or enabledValue == "true")
            commissionSettings.rate = employeeResult[1].fee_commission_rate or 0.1
            commissionSettings.minFeeAmount = employeeResult[1].fee_commission_min_amount or 500
            
            DebugPrint("^3[彩票系统] ^7员工手续费分成设置: enabled=" .. tostring(commissionSettings.enabled) ..
                        " (原始值=" .. tostring(enabledValue) .. ")" ..
                        ", rate=" .. tostring(commissionSettings.rate) ..
                        ", minAmount=" .. tostring(commissionSettings.minFeeAmount))
        else
            DebugPrint("^3[彩票系统] ^7未找到员工记录，使用默认手续费分成设置")
        end
    
        -- 根据票据类型确定表名和查询条件
        local tableName, whereClause
        local actualLotteryType = nil
        
        if lotteryType == 'scratch' then
            tableName = 'scratch_cards'
            whereClause = 'id = ? AND is_claimed = 0'
        elseif lotteryType == 'lottery' then
            tableName = 'lottery_tickets'
            whereClause = 'id = ? AND is_claimed = 0 AND is_winning = 1'
        else
            SystemPrint("^1[彩票系统] ^7兑奖失败: 未知票据类型")
            TriggerClientEvent('lottery:notification', source, "兑奖失败", "未知票据类型", "error")
            return
        end
        
        -- 查询奖品记录
        MySQL.Async.fetchAll('SELECT * FROM ' .. tableName .. ' WHERE ' .. whereClause, {ticketId}, function(result)
            if not result or #result == 0 then
                SystemPrint("^1[彩票系统] ^7兑奖失败: 未找到有效票据")
                TriggerClientEvent('lottery:notification', source, "兑奖失败", "未找到有效票据", "error")
                return
            end
            
            local prizeRecord = result[1]
            local prizeAmount = prizeRecord.prize_amount
            
            if lotteryType == 'lottery' then
                actualLotteryType = prizeRecord.lottery_type
            end
            
            -- 如果是刮刮乐类型，需要检查账户余额
            if lotteryType == 'scratch' then
                -- 计算手续费和实际支付金额
                local feeRate = 0  -- 手续费比例
                local feeAmount = 0  -- 手续费金额
                
                -- 根据奖金金额确定手续费比例
                if prizeAmount >= 10000 then
                    feeRate = 0.1
                end
                
                -- 计算手续费金额和实际支付金额
                feeAmount = math.floor(prizeAmount * feeRate)
                local actualPrizeAmount = prizeAmount - feeAmount
                
                SystemPrint("^3[彩票系统] ^7刮刮乐兑奖: 奖金=" .. prizeAmount .. ", 手续费率=" .. feeRate .. ", 手续费=" .. feeAmount .. ", 实际支付=" .. actualPrizeAmount)
                
                -- 检查彩票店账户余额
                if Config.Framework == "ESX" then
                    -- 使用ESX_addonaccount API检查账户余额
                    TriggerEvent('esx_addonaccount:getSharedAccount', 'society_lottery', function(societyAccount)
                        -- 添加调试日志，输出账户状态和余额
                        if societyAccount then
                            SystemPrint("^3[彩票系统-调试] ^7账户状态检查 - society_lottery账户余额: " .. societyAccount.money .. ", 需要支付: " .. actualPrizeAmount)
                        else
                            SystemPrint("^1[彩票系统-调试] ^7严重错误: society_lottery账户对象为空!")
                        end
                        
                        if societyAccount and societyAccount.money >= actualPrizeAmount then
                            -- 账户余额足够，从账户扣除实际支付金额
                            societyAccount.removeMoney(actualPrizeAmount)
                            -- 扣款成功，执行兑奖流程
                            ProcessAdminClaimPrize(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, feeRate, feeAmount)
                            
                            -- 处理员工手续费分成
                            if feeAmount > 0 and commissionSettings.enabled and feeAmount >= commissionSettings.minFeeAmount then
                                SystemPrint("^2[彩票系统] ^7手续费分成条件满足: 手续费=" .. feeAmount .. 
                                           ", 启用=" .. tostring(commissionSettings.enabled) .. 
                                           ", 最小金额=" .. tostring(commissionSettings.minFeeAmount))
                                
                                local staffCommission = math.floor(feeAmount * commissionSettings.rate)
                                if staffCommission > 0 then
                                    -- 获取玩家信息
                                    local playerSource = GetPlayerSourceByIdentifier(playerIdentifier)
                                    local playerName = "未知玩家"
                                    
                                    -- 从彩票店账户中扣除分成金额
                                    TriggerEvent('esx_addonaccount:getSharedAccount', 'society_lottery', function(bonusAccount)
                                        if bonusAccount then
                                            -- 尝试扣款
                                            local previousMoney = bonusAccount.money
                                            bonusAccount.removeMoney(staffCommission)
                                            
                                            -- 验证扣款是否成功
                                            local deductSuccess = true
                                            if bonusAccount.money == (previousMoney - staffCommission) then
                                                deductSuccess = true
                                            end
                                            
                                            if deductSuccess then
                                                -- 扣款成功，将奖金增加到员工的奖金字段中
                                                -- 恢复原有逻辑，增加到员工的奖金字段中
                                                MySQL.Sync.execute('UPDATE lottery_employees SET bonus = bonus + ? WHERE employee_id = ?', 
                                                    {staffCommission, playerIdentifier})
                                                
                                                -- 获取玩家名称用于记录
                                                local playerName = GetPlayerName(source)
                                                if not playerName then
                                                    local playerNameResult = MySQL.Sync.fetchScalar('SELECT player_name FROM lottery_tickets WHERE player_id = ? LIMIT 1', {playerIdentifier})
                                                    if playerNameResult then
                                                        playerName = playerNameResult
                                                    else
                                                        playerName = "未知玩家"
                                                    end
                                                end
                                                
                                                SystemPrint("^2[彩票系统] ^7员工手续费分成已添加到奖金: 手续费=" .. feeAmount .. 
                                                          ", 分成比例=" .. (commissionSettings.rate * 100) .. "%, 分成金额=" .. staffCommission ..
                                                          ", 已从彩票店账户扣除")
                                                
                                                -- 记录员工分成交易
                                                LogTransaction('commission', -staffCommission, '手续费分成奖励', playerIdentifier, playerName)
                                                
                                                -- 强制刷新账户余额，确保账户余额立即更新
                                                ForceRefreshAccountBalance()
                                                
                                                -- 向客户端发送分成信息
                                                local playerSource = GetPlayerSourceByIdentifier(playerIdentifier)
                                                if playerSource then
                                                    TriggerClientEvent('lottery:notification', playerSource, "手续费分成", "您获得了¥" .. staffCommission .. "的手续费分成奖励", "success")
                                                end
                                                
                                                -- 向客户端发送员工奖金更新通知
                                                TriggerClientEvent('lottery:employeeUpdate', -1, {
                                                    type = 'bonus',
                                                    employee_id = playerIdentifier,
                                                    employee_name = playerName,
                                                    bonus = staffCommission
                                                })
                                            else
                                                -- 扣款失败，检查是否启用职业系统
                                                if Config.LotteryJob and Config.LotteryJob.enabled then
                                                    -- 职业系统启用，提示玩家联系彩票店工作人员
                                                    SendNotification(source, "分成失败", "账户余额不足，手续费分成失败", "error")
                                                else
                                                    -- 职业系统未启用，尝试增加到员工奖金字段中
                                                    MySQL.Sync.execute('UPDATE lottery_employees SET bonus = bonus + ? WHERE employee_id = ?', 
                                                        {staffCommission, playerIdentifier})
                                                    
                                                    -- 获取玩家名称用于记录
                                                    local playerName = GetPlayerName(source)
                                                    if not playerName then
                                                        local playerNameResult = MySQL.Sync.fetchScalar('SELECT player_name FROM lottery_tickets WHERE player_id = ? LIMIT 1', {playerIdentifier})
                                                        if playerNameResult then
                                                            playerName = playerNameResult
                                                        else
                                                            playerName = "未知玩家"
                                                        end
                                                    end
                                                    
                                                    -- 记录员工分成交易
                                                    LogTransaction('commission', -staffCommission, '手续费分成奖励(无账户扣除)', playerIdentifier, playerName)
                                                    
                                                    -- 向客户端发送员工奖金更新通知
                                                    TriggerClientEvent('lottery:employeeUpdate', -1, {
                                                        type = 'bonus',
                                                        employee_id = playerIdentifier,
                                                        employee_name = playerName,
                                                        bonus = staffCommission
                                                    })
                                                    
                                                    -- 向客户端发送分成信息
                                                    local playerSource = GetPlayerSourceByIdentifier(playerIdentifier)
                                                    if playerSource then
                                                        TriggerClientEvent('lottery:notification', playerSource, "手续费分成", "您获得了¥" .. staffCommission .. "的手续费分成奖励", "success")
                                                    end
                                                end
                                            end
                                        end
                                    end)
                                else
                                    SystemPrint("^3[彩票系统] ^7员工手续费分成金额为0，不添加奖金")
                                end
                            else
                                SystemPrint("^3[彩票系统] ^7不满足员工手续费分成条件: 启用=" .. tostring(commissionSettings.enabled) .. 
                                            ", 手续费=" .. feeAmount .. ", 最小金额=" .. commissionSettings.minFeeAmount)
                            end
                            
                            -- 刷新管理系统数据
                            TriggerClientEvent('lottery:refreshAdminData', -1)
                        else
                            -- 扣款失败
                            SystemPrint("^1[彩票系统] ^7彩票店账户扣款失败")
                            TriggerClientEvent('lottery:notification', source, "兑奖失败", "账户余额不足请联系老板", "error")
                        end
                    end)
                else -- Config.Framework == "QB"
                    -- QBCore版本使用直接数据库查询获取账户余额
                    MySQL.Async.fetchAll('SELECT balance FROM lottery_shop_accounts WHERE id = 1', {}, function(result)
                        local balance = 0
                        if result and #result > 0 then
                            balance = result[1].balance
                        end
                        
                        SystemPrint("^3[彩票系统-调试] ^7账户状态检查 - lottery_shop_accounts账户余额: " .. balance .. ", 需要支付: " .. actualPrizeAmount)
                        
                        if balance >= actualPrizeAmount then
                            -- 账户余额足够，从账户扣除实际支付金额
                            MySQL.Async.execute('UPDATE lottery_shop_accounts SET balance = balance - ? WHERE id = 1', {actualPrizeAmount}, function(rowsChanged)
                                if rowsChanged > 0 then
                                    -- 扣款成功，执行兑奖流程
                                    ProcessAdminClaimPrize(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, feeRate, feeAmount)
                                    
                                    -- 处理员工手续费分成
                                    if feeAmount > 0 and commissionSettings.enabled and feeAmount >= commissionSettings.minFeeAmount then
                                        SystemPrint("^2[彩票系统] ^7手续费分成条件满足: 手续费=" .. feeAmount .. 
                                                   ", 启用=" .. tostring(commissionSettings.enabled) .. 
                                                   ", 最小金额=" .. tostring(commissionSettings.minFeeAmount))
                                        
                                        local staffCommission = math.floor(feeAmount * commissionSettings.rate)
                                        if staffCommission > 0 then
                                            -- 获取玩家信息
                                            local playerSource = GetPlayerSourceByIdentifier(playerIdentifier)
                                            local playerName = "未知玩家"
                                            
                                            -- 从彩票店账户中扣除分成金额
                                            MySQL.Async.execute('UPDATE lottery_shop_accounts SET balance = balance - ? WHERE id = 1', {staffCommission}, function(bonusRowsChanged)
                                                if bonusRowsChanged > 0 then
                                                    -- 扣款成功，将奖金增加到员工的奖金字段中
                                                    MySQL.Sync.execute('UPDATE lottery_employees SET bonus = bonus + ? WHERE employee_id = ?', 
                                                        {staffCommission, playerIdentifier})
                                                    
                                                    -- 获取玩家名称用于记录
                                                    local playerName = GetPlayerName(source)
                                                    if not playerName then
                                                        local playerNameResult = MySQL.Sync.fetchScalar('SELECT player_name FROM lottery_tickets WHERE player_id = ? LIMIT 1', {playerIdentifier})
                                                        if playerNameResult then
                                                            playerName = playerNameResult
                                                        else
                                                            playerName = "未知玩家"
                                                        end
                                                    end
                                                    
                                                    SystemPrint("^2[彩票系统] ^7员工手续费分成已添加到奖金: 手续费=" .. feeAmount .. 
                                                              ", 分成比例=" .. (commissionSettings.rate * 100) .. "%, 分成金额=" .. staffCommission ..
                                                              ", 已从彩票店账户扣除")
                                                    
                                                    -- 记录员工分成交易
                                                    LogTransaction('commission', -staffCommission, '手续费分成奖励', playerIdentifier, playerName)
                                                    
                                                    -- 强制刷新账户余额，确保账户余额立即更新
                                                    ForceRefreshAccountBalance()
                                                    
                                                    -- 向客户端发送分成信息
                                                    local playerSource = GetPlayerSourceByIdentifier(playerIdentifier)
                                                    if playerSource then
                                                        TriggerClientEvent('lottery:notification', playerSource, "手续费分成", "您获得了¥" .. staffCommission .. "的手续费分成奖励", "success")
                                                    end
                                                    
                                                    -- 向客户端发送员工奖金更新通知
                                                    TriggerClientEvent('lottery:employeeUpdate', -1, {
                                                        type = 'bonus',
                                                        employee_id = playerIdentifier,
                                                        employee_name = playerName,
                                                        bonus = staffCommission
                                                    })
                                                else
                                                    -- 扣款失败，处理与ESX相同
                                                    SystemPrint("^1[彩票系统] ^7彩票店账户扣款失败 - 员工手续费分成")
                                                    -- 检查是否启用职业系统
                                                    if Config.LotteryJob and Config.LotteryJob.enabled then
                                                        -- 职业系统启用，提示玩家联系彩票店工作人员
                                                        SendNotification(source, "分成失败", "账户余额不足，手续费分成失败", "error")
                                                    else
                                                        -- 职业系统未启用，尝试增加到员工奖金字段中
                                                        MySQL.Sync.execute('UPDATE lottery_employees SET bonus = bonus + ? WHERE employee_id = ?', 
                                                            {staffCommission, playerIdentifier})
                                                        
                                                        -- 获取玩家名称用于记录
                                                        local playerName = GetPlayerName(source)
                                                        if not playerName then
                                                            local playerNameResult = MySQL.Sync.fetchScalar('SELECT player_name FROM lottery_tickets WHERE player_id = ? LIMIT 1', {playerIdentifier})
                                                            if playerNameResult then
                                                                playerName = playerNameResult
                                                            else
                                                                playerName = "未知玩家"
                                                            end
                                                        end
                                                        
                                                        -- 记录员工分成交易
                                                        LogTransaction('commission', -staffCommission, '手续费分成奖励(无账户扣除)', playerIdentifier, playerName)
                                                        
                                                        -- 向客户端发送员工奖金更新通知
                                                        TriggerClientEvent('lottery:employeeUpdate', -1, {
                                                            type = 'bonus',
                                                            employee_id = playerIdentifier,
                                                            employee_name = playerName,
                                                            bonus = staffCommission
                                                        })
                                                        
                                                        -- 向客户端发送分成信息
                                                        local playerSource = GetPlayerSourceByIdentifier(playerIdentifier)
                                                        if playerSource then
                                                            TriggerClientEvent('lottery:notification', playerSource, "手续费分成", "您获得了¥" .. staffCommission .. "的手续费分成奖励", "success")
                                                        end
                                                    end
                                                end
                                            end)
                                        else
                                            SystemPrint("^3[彩票系统] ^7员工手续费分成金额为0，不添加奖金")
                                        end
                                    else
                                        SystemPrint("^3[彩票系统] ^7不满足员工手续费分成条件: 启用=" .. tostring(commissionSettings.enabled) .. 
                                                    ", 手续费=" .. feeAmount .. ", 最小金额=" .. commissionSettings.minFeeAmount)
                                    end
                                    
                                    -- 刷新管理系统数据
                                    TriggerClientEvent('lottery:refreshAdminData', -1)
                                else
                                    -- 扣款失败
                                    SystemPrint("^1[彩票系统] ^7彩票店账户扣款失败")
                                    TriggerClientEvent('lottery:notification', source, "兑奖失败", "账户余额不足请联系老板", "error")
                                end
                            end)
                        else
                            -- 余额不足
                            SystemPrint("^1[彩票系统] ^7彩票店账户余额不足")
                            TriggerClientEvent('lottery:notification', source, "兑奖失败", "账户余额不足请联系老板", "error")
                        end
                    end)
                end
            else
                -- 非刮刮乐类型，先计算手续费再处理兑奖
                local feeRate = 0
                local feeAmount = 0

                if lotteryType == 'lottery' and (actualLotteryType == 'double_ball' or actualLotteryType == 'super_lotto') then
                    -- 计算手续费金额
                    if prizeAmount >= 500000 then
                        -- 大额彩票奖金：计算20%的手续费
                        feeRate = 0.2
                    end

                    feeAmount = math.floor(prizeAmount * feeRate)

                    DebugPrint("^3[彩票系统] ^7" .. actualLotteryType .. "兑奖手续费计算: 奖金=" .. prizeAmount .. ", 手续费率=" .. (feeRate * 100) .. "%, 手续费金额=" .. feeAmount)

                    -- 使用正确的手续费参数处理兑奖
                    ProcessAdminClaimPrize(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, feeRate, feeAmount)

                    -- 将手续费增加到账户余额
                    if feeAmount > 0 then
                        if Config.Framework == "ESX" then
                            -- 使用ESX addonaccount API添加手续费到彩票店账户
                            TriggerEvent('esx_addonaccount:getSharedAccount', 'society_lottery', function(societyAccount)
                                societyAccount.addMoney(feeAmount)
                                DebugPrint("^2[彩票系统] ^7" .. actualLotteryType .. "兑奖手续费已增加到账户余额: 金额=" .. feeAmount)
                            end)
                        else -- Config.Framework == "QB"
                            -- 使用直接数据库查询添加手续费到lottery_shop_accounts表
                            MySQL.Sync.execute('UPDATE lottery_shop_accounts SET balance = balance + ? WHERE id = 1', {feeAmount})
                            DebugPrint("^2[彩票系统] ^7" .. actualLotteryType .. "兑奖手续费已增加到账户余额: 金额=" .. feeAmount)
                        end
                        
                        -- 处理员工手续费分成
                        if commissionSettings.enabled and feeAmount >= commissionSettings.minFeeAmount then
                            SystemPrint("^2[彩票系统] ^7手续费分成条件满足: 彩票类型=" .. actualLotteryType .. 
                                    ", 手续费=" .. feeAmount .. 
                                    ", 启用=" .. tostring(commissionSettings.enabled) .. 
                                    ", 最小金额=" .. tostring(commissionSettings.minFeeAmount))
                            
                            local staffCommission = math.floor(feeAmount * commissionSettings.rate)
                            if staffCommission > 0 then
                                if Config.Framework == "ESX" then
                                    -- 使用esx_addonaccount API操作账户
                                    TriggerEvent('esx_addonaccount:getSharedAccount', 'society_lottery', function(societyAccount)
                                        -- 尝试扣款
                                        local previousMoney = societyAccount.money
                                        societyAccount.removeMoney(staffCommission)
                                        
                                        -- 验证扣款是否成功
                                        local deductSuccess = true
                                        if societyAccount.money == (previousMoney - staffCommission) then
                                            deductSuccess = true
                                        end
                                        
                                        if deductSuccess then
                                            -- 扣款成功，将奖金增加到员工的奖金字段中
                                            -- 恢复原有逻辑，增加到员工的奖金字段中
                                            MySQL.Sync.execute('UPDATE lottery_employees SET bonus = bonus + ? WHERE employee_id = ?', 
                                                {staffCommission, playerIdentifier})
                                            
                                            -- 获取玩家名称用于记录
                                            local playerName = GetPlayerName(source)
                                            if not playerName then
                                                local playerNameResult = MySQL.Sync.fetchScalar('SELECT player_name FROM lottery_tickets WHERE player_id = ? LIMIT 1', {playerIdentifier})
                                                if playerNameResult then
                                                    playerName = playerNameResult
                                                else
                                                    playerName = "未知玩家"
                                                end
                                            end
                                            
                                            SystemPrint("^2[彩票系统] ^7员工手续费分成已添加到奖金: 手续费=" .. feeAmount .. 
                                                      ", 分成比例=" .. (commissionSettings.rate * 100) .. "%, 分成金额=" .. staffCommission ..
                                                      ", 已从彩票店账户扣除")
                                            
                                            -- 记录员工分成交易
                                            LogTransaction('commission', -staffCommission, '手续费分成奖励', playerIdentifier, playerName)
                                            
                                            -- 强制刷新账户余额，确保账户余额立即更新
                                            ForceRefreshAccountBalance()
                                            
                                            -- 向客户端发送分成信息
                                            local playerSource = GetPlayerSourceByIdentifier(playerIdentifier)
                                            if playerSource then
                                                TriggerClientEvent('lottery:notification', playerSource, "手续费分成", "您获得了¥" .. staffCommission .. "的手续费分成奖励", "success")
                                            end
                                            
                                            -- 向客户端发送员工奖金更新通知
                                            TriggerClientEvent('lottery:employeeUpdate', -1, {
                                                type = 'bonus',
                                                employee_id = playerIdentifier,
                                                employee_name = playerName,
                                                bonus = staffCommission
                                            })
                                        else
                                            -- 扣款失败
                                            SystemPrint("^1[彩票系统] ^7彩票店账户扣款失败 - 员工手续费分成")
                                            -- 检查是否启用职业系统
                                            if Config.LotteryJob and Config.LotteryJob.enabled then
                                                -- 职业系统启用，提示玩家联系彩票店工作人员
                                                SendNotification(source, "分成失败", "账户余额不足，手续费分成失败", "error")
                                            else
                                                -- 职业系统未启用，尝试增加到员工奖金字段中
                                                MySQL.Sync.execute('UPDATE lottery_employees SET bonus = bonus + ? WHERE employee_id = ?', 
                                                    {staffCommission, playerIdentifier})
                                                
                                                -- 获取玩家名称用于记录
                                                local playerName = GetPlayerName(source)
                                                if not playerName then
                                                    local playerNameResult = MySQL.Sync.fetchScalar('SELECT player_name FROM lottery_tickets WHERE player_id = ? LIMIT 1', {playerIdentifier})
                                                    if playerNameResult then
                                                        playerName = playerNameResult
                                                    else
                                                        playerName = "未知玩家"
                                                    end
                                                end
                                                
                                                -- 记录员工分成交易
                                                LogTransaction('commission', -staffCommission, '手续费分成奖励(无账户扣除)', playerIdentifier, playerName)
                                                
                                                -- 向客户端发送员工奖金更新通知
                                                TriggerClientEvent('lottery:employeeUpdate', -1, {
                                                    type = 'bonus',
                                                    employee_id = playerIdentifier,
                                                    employee_name = playerName,
                                                    bonus = staffCommission
                                                })
                                                
                                                -- 向客户端发送分成信息
                                                local playerSource = GetPlayerSourceByIdentifier(playerIdentifier)
                                                if playerSource then
                                                    TriggerClientEvent('lottery:notification', playerSource, "手续费分成", "您获得了¥" .. staffCommission .. "的手续费分成奖励", "success")
                                                end
                                            end
                                        end
                                    end)
                                else -- Config.Framework == "QB"
                                    -- 使用直接数据库查询扣除分成金额
                                    MySQL.Async.execute('UPDATE lottery_shop_accounts SET balance = balance - ? WHERE id = 1', {staffCommission}, function(rowsChanged)
                                        local deductSuccess = (rowsChanged > 0)
                                        
                                        if deductSuccess then
                                            -- 扣款成功，将奖金增加到员工的奖金字段中
                                            MySQL.Sync.execute('UPDATE lottery_employees SET bonus = bonus + ? WHERE employee_id = ?', 
                                                {staffCommission, playerIdentifier})
                                            
                                            -- 获取玩家名称用于记录
                                            local playerName = GetPlayerName(source)
                                            if not playerName then
                                                local playerNameResult = MySQL.Sync.fetchScalar('SELECT player_name FROM lottery_tickets WHERE player_id = ? LIMIT 1', {playerIdentifier})
                                                if playerNameResult then
                                                    playerName = playerNameResult
                                                else
                                                    playerName = "未知玩家"
                                                end
                                            end
                                            
                                            SystemPrint("^2[彩票系统] ^7员工手续费分成已添加到奖金: 手续费=" .. feeAmount .. 
                                                      ", 分成比例=" .. (commissionSettings.rate * 100) .. "%, 分成金额=" .. staffCommission ..
                                                      ", 已从彩票店账户扣除")
                                            
                                            -- 记录员工分成交易
                                            LogTransaction('commission', -staffCommission, '手续费分成奖励', playerIdentifier, playerName)
                                            
                                            -- 强制刷新账户余额，确保账户余额立即更新
                                            ForceRefreshAccountBalance()
                                            
                                            -- 向客户端发送分成信息
                                            local playerSource = GetPlayerSourceByIdentifier(playerIdentifier)
                                            if playerSource then
                                                TriggerClientEvent('lottery:notification', playerSource, "手续费分成", "您获得了¥" .. staffCommission .. "的手续费分成奖励", "success")
                                            end
                                            
                                            -- 向客户端发送员工奖金更新通知
                                            TriggerClientEvent('lottery:employeeUpdate', -1, {
                                                type = 'bonus',
                                                employee_id = playerIdentifier,
                                                employee_name = playerName,
                                                bonus = staffCommission
                                            })
                                        else
                                            -- 扣款失败
                                            SystemPrint("^1[彩票系统] ^7彩票店账户扣款失败 - 员工手续费分成")
                                            -- 检查是否启用职业系统
                                            if Config.LotteryJob and Config.LotteryJob.enabled then
                                                -- 职业系统启用，提示玩家联系彩票店工作人员
                                                SendNotification(source, "分成失败", "账户余额不足，手续费分成失败", "error")
                                            else
                                                -- 职业系统未启用，尝试增加到员工奖金字段中
                                                MySQL.Sync.execute('UPDATE lottery_employees SET bonus = bonus + ? WHERE employee_id = ?', 
                                                    {staffCommission, playerIdentifier})
                                                
                                                -- 获取玩家名称用于记录
                                                local playerName = GetPlayerName(source)
                                                if not playerName then
                                                    local playerNameResult = MySQL.Sync.fetchScalar('SELECT player_name FROM lottery_tickets WHERE player_id = ? LIMIT 1', {playerIdentifier})
                                                    if playerNameResult then
                                                        playerName = playerNameResult
                                                    else
                                                        playerName = "未知玩家"
                                                    end
                                                end
                                                
                                                -- 记录员工分成交易
                                                LogTransaction('commission', -staffCommission, '手续费分成奖励(无账户扣除)', playerIdentifier, playerName)
                                                
                                                -- 向客户端发送员工奖金更新通知
                                                TriggerClientEvent('lottery:employeeUpdate', -1, {
                                                    type = 'bonus',
                                                    employee_id = playerIdentifier,
                                                    employee_name = playerName,
                                                    bonus = staffCommission
                                                })
                                                
                                                -- 向客户端发送分成信息
                                                local playerSource = GetPlayerSourceByIdentifier(playerIdentifier)
                                                if playerSource then
                                                    TriggerClientEvent('lottery:notification', playerSource, "手续费分成", "您获得了¥" .. staffCommission .. "的手续费分成奖励", "success")
                                                end
                                            end
                                        end
                                    end)
                                end
                            else
                                SystemPrint("^3[彩票系统] ^7员工手续费分成金额为0，不添加奖金")
                            end
                        else
                            DebugPrint("^3[彩票系统] ^7不满足员工手续费分成条件: 彩票类型=" .. actualLotteryType ..
                                        ", 启用=" .. tostring(commissionSettings.enabled) ..
                                        ", 手续费=" .. feeAmount .. ", 最小金额=" .. commissionSettings.minFeeAmount)
                        end
                    end

                    -- 刷新管理系统数据
                    TriggerClientEvent('lottery:refreshAdminData', -1)
                else
                    -- 非大乐透或双色球类型，直接处理兑奖
                    ProcessAdminClaimPrize(source, ticketId, lotteryType, tableName, prizeRecord, prizeAmount, playerIdentifier, actualLotteryType, 0, 0)
                    -- 刷新管理系统数据
                    TriggerClientEvent('lottery:refreshAdminData', -1)
                end
            end
        end)
    end)
end)

-- 添加新函数：强制刷新账户余额
function ForceRefreshAccountBalance()
    -- 清除账户缓存
    ClearCache("shopAccount")
    ClearCache("transactions")
    
    -- 通知所有玩家刷新管理系统数据
    TriggerClientEvent('lottery:refreshAdminData', -1)
    
    -- 延迟300ms后再次强制刷新，确保数据库操作已完成
    Citizen.SetTimeout(300, function()
        ClearCache("shopAccount")
        TriggerClientEvent('lottery:refreshAdminData', -1)
    end)
end

-- 在玩家连接时检查离线奖金
AddEventHandler('playerConnecting', function(playerName, setKickReason, deferrals)
    local source = source
    local identifier = GetPlayerIdentifier(source)
    
    if not identifier then return end
    
    -- 延迟处理，确保玩家完全加载
    Citizen.SetTimeout(5000, function()
        CheckOfflinePrizes(source, identifier)
    end)
end)

-- 检查玩家是否有离线奖金需要领取
function CheckOfflinePrizes(source, identifier)
    if not identifier then 
        identifier = GetPlayerIdentifier(source)
        if not identifier then return end
    end
    
    -- 查询该玩家是否有未领取的奖金
    MySQL.Async.fetchAll('SELECT * FROM lottery_offline_prizes WHERE player_id = ? AND claimed = 0', {identifier}, function(result)
        if result and #result > 0 then
            local totalPrize = 0
            local itemRewards = {}
            local cashPrizes = {}

            -- 分类处理不同类型的奖励
            for _, prize in ipairs(result) do
                if string.find(prize.prize_type, "排列5%-物品奖励:") then
                    -- 排列5物品奖励
                    local rewardDataStr = string.gsub(prize.prize_type, "排列5%-物品奖励:", "")
                    local rewardData = json.decode(rewardDataStr)
                    if rewardData and rewardData.item and rewardData.amount then
                        table.insert(itemRewards, {
                            id = prize.id,
                            item = rewardData.item,
                            amount = rewardData.amount,
                            displayName = rewardData.displayName or rewardData.item
                        })
                    end
                else
                    -- 现金奖励
                    totalPrize = totalPrize + prize.prize_amount
                    table.insert(cashPrizes, prize.id)
                end
            end

            -- 处理物品奖励
            local itemsGiven = {}
            local allItemsSuccess = true
            for _, reward in ipairs(itemRewards) do
                local success = GivePlayerItem(source, reward.item, reward.amount)
                if success then
                    local displayName = Config.Items.itemNames[reward.item] or reward.item
                    local rewardText = ""
                    if reward.item == "money" then
                        rewardText = displayName .. " ¥" .. reward.amount
                    else
                        rewardText = displayName .. " x" .. reward.amount
                    end
                    table.insert(itemsGiven, rewardText)

                    -- 标记该物品奖励为已领取
                    MySQL.Async.execute('UPDATE lottery_offline_prizes SET claimed = 1 WHERE id = ?', {reward.id})
                    DebugPrint("^2[彩票系统] ^7离线排列5物品奖励发放成功: " .. rewardText)
                else
                    allItemsSuccess = false
                    DebugPrint("^1[彩票系统] ^7离线排列5物品奖励发放失败: " .. reward.item .. " x" .. reward.amount)
                end
            end

            -- 处理现金奖励
            if totalPrize > 0 then
                -- 将现金奖励记录标记为已领取
                if #cashPrizes > 0 then
                    local prizeIds = table.concat(cashPrizes, ',')
                    MySQL.Async.execute('UPDATE lottery_offline_prizes SET claimed = 1 WHERE id IN (' .. prizeIds .. ')', {}, function()
                        -- 给玩家发放奖金
                        AddPlayerMoney(source, totalPrize)

                        -- 通知玩家现金奖励
                        TriggerClientEvent('lottery:notification', source, '离线奖金', '您收到了离线兑奖奖金共计: ¥' .. totalPrize, 'success')
                        SystemPrint("^2[彩票系统] ^7玩家 " .. GetPlayerName(source) .. " 收到离线奖金: ¥" .. totalPrize)
                    end)
                end
            end

            -- 通知玩家物品奖励
            if #itemsGiven > 0 then
                local itemText = table.concat(itemsGiven, ", ")
                TriggerClientEvent('lottery:notification', source, '离线奖品', '您收到了离线兑奖奖品: ' .. itemText, 'success')
                SystemPrint("^2[彩票系统] ^7玩家 " .. GetPlayerName(source) .. " 收到离线物品奖励: " .. itemText)
            end
        end
    end)
end