-- 彩票系统服务器主文件
local ESX, QBCore = nil, nil
local Framework = nil
local SystemConfigLoaded = false
-- 不再需要LoadConfigFromFile函数引用，直接使用config.lua中的配置

-- 调试打印函数 - 只在调试模式开启时打印
local function DebugPrint(message, level)
    if Config.Debug and Config.Debug.enabled then
        level = level or "^7" -- 默认白色
        print(level .. "[彩票系统-调试] " .. message)
    end
end

-- 系统消息打印函数 - 总是打印重要的系统消息
local function SystemPrint(message, level)
    level = level or "^7" -- 默认白色
    print(level .. "[彩票系统] " .. message)
end

-- 框架初始化
CreateThread(function()
    -- 如果Config.Framework已经设置，则使用该设置
    if Config.Framework == 'ESX' then
        ESX = exports['es_extended']:getSharedObject()
        Framework = 'ESX'
        print("^2[彩票系统] ^7使用配置的框架: ESX")
    elseif Config.Framework == 'QB' then
        QBCore = exports['qb-core']:GetCoreObject()
        Framework = 'QB'
        print("^2[彩票系统] ^7使用配置的框架: QB-Core")
    else
        -- 自动检测框架
        if GetResourceState('es_extended') == 'started' then
            ESX = exports['es_extended']:getSharedObject()
            Framework = 'ESX'
            Config.Framework = 'ESX'
            print("^2[彩票系统] ^7自动检测到框架: ESX")
        elseif GetResourceState('qb-core') == 'started' then
            QBCore = exports['qb-core']:GetCoreObject()
            Framework = 'QB'
            Config.Framework = 'QB'
            print("^2[彩票系统] ^7自动检测到框架: QB-Core")
        end
    end
    
    if not Framework then
        print("^1[彩票系统] ^7错误: 未检测到支持的框架(ESX或QB-Core)")
        return
    end
    
    -- 立即加载配置覆盖（如果存在）
    LoadConfigOverride()

    -- 初始化数据库
    SetTimeout(1000, function()
        if InitializeDatabase then
            InitializeDatabase()
        else
            print("^1[彩票系统] ^7数据库初始化函数未找到")
        end
    end)

    -- 初始化奖池
    InitializePrizePools()
    
    -- 注册彩票店职业（如果不存在）
    RegisterLotteryShopJob()
    
    -- 添加检查彩票店账户是否存在的调试日志
    Citizen.SetTimeout(3000, function()
        if Config.Framework == 'ESX' then
            -- ESX框架使用addon_account_data表
            MySQL.Async.fetchAll('SELECT * FROM addon_account_data WHERE account_name = ?', {'society_lottery'}, function(result)
                if result and #result > 0 then
                    print("^2[彩票系统] ^7Society_lottery账户已存在，当前余额: " .. result[1].money)
                else
                    print("^1[彩票系统] ^7警告: Society_lottery账户不存在！请确保已安装lottery_account.sql")
                end
            end)
        elseif Config.Framework == 'QB' then
            -- QB框架使用lottery_shop_accounts表
            MySQL.Async.fetchAll('SELECT * FROM lottery_shop_accounts WHERE id = 1', {}, function(result)
                if result and #result > 0 then
                    print("^2[彩票系统] ^7彩票店账户已存在，当前余额: " .. result[1].balance)
                else
                    print("^1[彩票系统] ^7警告: 彩票店账户不存在！请确保已安装lottery_shop_accounts.sql")
                end
            end)
        end
    end)
end)

-- 注册彩票店职业
function RegisterLotteryShopJob()
    if Config.Framework == 'ESX' then
        -- 检查ESX职业是否存在
        MySQL.Async.fetchAll('SELECT * FROM jobs WHERE name = ?', {Config.LotteryJob.name}, function(result)
            if not result or #result == 0 then
                -- 职业不存在，创建职业
                MySQL.Async.execute('INSERT INTO jobs (name, label) VALUES (?, ?)', 
                    {Config.LotteryJob.name, Config.LotteryJob.label}, 
                    function()
                        print('^2[彩票系统] ^7成功创建彩票店职业')
                        
                        -- 创建职业等级
                        for _, grade in ipairs(Config.LotteryJob.grades) do
                            -- ESX需要salary字段，设置为0
                            MySQL.Async.execute('INSERT INTO job_grades (job_name, grade, name, label, salary, skin_male, skin_female) VALUES (?, ?, ?, ?, ?, ?, ?)', 
                                {Config.LotteryJob.name, grade.grade, grade.name, grade.label, 0, '{}', '{}'}, 
                                function()
                                    print('^2[彩票系统] ^7成功创建彩票店职业等级: ' .. grade.label)
                                end
                            )
                        end
                    end
                )
            else
                print('^2[彩票系统] ^7彩票店职业已存在')
            end
        end)
    elseif Config.Framework == 'QB' then
        -- 检查QB职业是否存在
        local jobExists = false
        for jobName, _ in pairs(QBCore.Shared.Jobs) do
            if jobName == Config.LotteryJob.name then
                jobExists = true
                break
            end
        end
        
        if not jobExists then
            -- 创建职业
            local jobGrades = {}
            for _, grade in ipairs(Config.LotteryJob.grades) do
                jobGrades[tostring(grade.grade)] = {
                    name = grade.label,
                    isboss = grade.isBoss or false
                }
            end
            
            QBCore.Shared.Jobs[Config.LotteryJob.name] = {
                label = Config.LotteryJob.label,
                grades = jobGrades
            }
            print('^2[彩票系统] ^7成功创建彩票店职业')
        else
            print('^2[彩票系统] ^7彩票店职业已存在')
        end
    end
end

-- 获取玩家对象
function GetPlayer(source)
    if Config.Framework == 'ESX' then
        return ESX.GetPlayerFromId(source)
    elseif Config.Framework == 'QB' then
        return QBCore.Functions.GetPlayer(source)
    end
    return nil
end

-- 获取玩家标识符
function GetPlayerIdentifier(source)
    -- 首先尝试从框架获取
    if Config.Framework == 'ESX' then
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer and xPlayer.identifier then
            return xPlayer.identifier
        end
    elseif Config.Framework == 'QB' then
        local Player = QBCore.Functions.GetPlayer(source)
        if Player and Player.PlayerData and Player.PlayerData.citizenid then
            return Player.PlayerData.citizenid
        end
    end
    
    -- 如果框架方式失败，使用FiveM原生方法作为后备
    local identifiers = GetPlayerIdentifiers(source)
    if identifiers and #identifiers > 0 then
        -- 优先使用许可证标识符(license)
        for _, identifier in ipairs(identifiers) do
            if string.find(identifier, "license:") then
                return string.gsub(identifier, "license:", "")
            end
        end
        -- 如果没有许可证，使用第一个可用标识符
        return string.gsub(identifiers[1], "%w+:", "") -- 移除前缀
    end
    
    return "unknown_" .. tostring(source) -- 返回一个临时标识符防止数据库错误
end

-- 获取玩家金钱
function GetPlayerMoney(source)
    if Config.Framework == 'ESX' then
        local xPlayer = ESX.GetPlayerFromId(source)
        return xPlayer and xPlayer.getMoney() or 0
    elseif Config.Framework == 'QB' then
        local Player = QBCore.Functions.GetPlayer(source)
        return Player and Player.PlayerData.money.cash or 0
    end
    return 0
end

-- 移除玩家金钱
function RemovePlayerMoney(source, amount)
    if Config.Framework == 'ESX' then
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer then
            xPlayer.removeMoney(amount)
            return true
        end
    elseif Config.Framework == 'QB' then
        local Player = QBCore.Functions.GetPlayer(source)
        if Player then
            Player.Functions.RemoveMoney('cash', amount)
            return true
        end
    end
    return false
end

-- 添加玩家金钱
function AddPlayerMoney(source, amount)
    if Config.Framework == 'ESX' then
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer then
            xPlayer.addMoney(amount)
            return true
        end
    elseif Config.Framework == 'QB' then
        local Player = QBCore.Functions.GetPlayer(source)
        if Player then
            Player.Functions.AddMoney('cash', amount)
            return true
        end
    end
    return false
end

-- 发送通知
function SendNotification(source, message, type)
    type = type or 'info'

    -- 记录通知发送
    DebugPrint("^3[彩票系统] ^7发送通知给玩家 " .. source .. ": " .. message .. " (类型: " .. type .. ")")

    if Config.Framework == 'ESX' then
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer then
            xPlayer.showNotification(message)
        else
            -- 如果获取玩家失败，尝试直接发送事件
            TriggerClientEvent('esx:showNotification', source, message)
        end
    elseif Config.Framework == 'QB' then
        TriggerClientEvent('QBCore:Notify', source, message, type)
    else
        -- 未知框架，发送到聊天窗口
        TriggerClientEvent('chat:addMessage', source, {
            color = {100, 200, 255},
            multiline = false,
            args = {"彩票系统", message}
        })
    end
end

-- 发送全服通知
function SendServerNotification(message, type)
    type = type or 'info'

    -- 记录通知发送
    DebugPrint("^3[彩票系统] ^7发送全服通知: " .. message .. " (类型: " .. type .. ")")

    if Config.Framework == 'ESX' then
        -- ESX框架通知
        TriggerClientEvent('esx:showNotification', -1, message)
        -- 同时发送到聊天窗口确保可见性
        TriggerClientEvent('chat:addMessage', -1, {
            color = {100, 255, 100},
            multiline = false,
            args = {"彩票系统", message}
        })
    elseif Config.Framework == 'QB' then
        -- QB-Core框架通知
        TriggerClientEvent('QBCore:Notify', -1, message, type)
        -- 同时发送到聊天窗口确保可见性
        TriggerClientEvent('chat:addMessage', -1, {
            color = {100, 255, 100},
            multiline = false,
            args = {"彩票系统", message}
        })
    else
        -- 未知框架，只发送到聊天窗口
        TriggerClientEvent('chat:addMessage', -1, {
            color = {100, 255, 100},
            multiline = false,
            args = {"彩票系统", message}
        })
    end

    -- 记录到服务器日志
    DebugPrint("^2[彩票系统] ^7全服通知已发送: " .. message)
end

-- 获取玩家姓名
function GetPlayerName(source)
    if Config.Framework == 'ESX' then
        local xPlayer = ESX.GetPlayerFromId(source)
        return xPlayer and xPlayer.getName() or "未知玩家"
    elseif Config.Framework == 'QB' then
        local Player = QBCore.Functions.GetPlayer(source)
        return Player and Player.PlayerData.charinfo.firstname .. " " .. Player.PlayerData.charinfo.lastname or "未知玩家"
    end
    
    -- 如果框架方法失败，使用原生方法
    return GetPlayerName(source) or "未知玩家"
end

-- 检查玩家权限
function HasPermission(source, permission)
    -- 调试输出
    SystemPrint("^3[权限检查] ^7玩家ID: " .. source .. " 请求权限: " .. permission)
    
    if Config.Framework == 'ESX' then
        local xPlayer = ESX.GetPlayerFromId(source)
        if not xPlayer then return false end
        
        -- 检查是否为彩票店职业
        if permission == "lottery_shop" then
            local job = xPlayer.getJob()
            return job and job.name == Config.LotteryJob.name
        else
            -- 其他权限检查
            return xPlayer.getGroup() == permission
        end
    elseif Config.Framework == 'QB' then
        local Player = QBCore.Functions.GetPlayer(source)
        if not Player then 
            SystemPrint("^1[权限检查] ^7玩家不存在: " .. source)
            return false 
        end
        
        -- 检查是否为彩票店职业
        if permission == "lottery_shop" then
            local job = Player.PlayerData.job
            SystemPrint("^3[权限检查] ^7彩票店职业检查 - 玩家职业: " .. (job and job.name or "无") .. ", 需要职业: " .. Config.LotteryJob.name)
            return job and job.name == Config.LotteryJob.name
        -- 管理员权限检查
        elseif permission == "admin" or permission == "god" then
            -- 使用多种方式检查管理员权限以确保兼容性
            local hasAdmin = false
            local hasGod = false
            local hasMod = false

            -- 方法1: 使用QBCore.Functions.HasPermission (如果存在)
            if QBCore.Functions.HasPermission then
                hasAdmin = QBCore.Functions.HasPermission(source, 'admin')
                hasGod = QBCore.Functions.HasPermission(source, 'god')
                hasMod = QBCore.Functions.HasPermission(source, 'mod')
            end

            -- 方法2: 检查玩家数据中的权限组
            if not (hasGod or hasAdmin or hasMod) then
                if Player and Player.PlayerData then
                    -- 检查权限组
                    if Player.PlayerData.permission then
                        local playerPermission = Player.PlayerData.permission
                        hasGod = playerPermission == 'god'
                        hasAdmin = playerPermission == 'admin'
                        hasMod = playerPermission == 'mod'
                    end

                    -- 检查职业是否为管理员相关
                    if Player.PlayerData.job and Player.PlayerData.job.name then
                        local jobName = Player.PlayerData.job.name
                        if jobName == 'admin' or jobName == 'police' or jobName == 'government' then
                            hasAdmin = true
                        end
                    end
                end
            end

            -- 方法3: 使用IsPlayerAceAllowed (FiveM原生权限系统)
            if not (hasGod or hasAdmin or hasMod) then
                hasGod = IsPlayerAceAllowed(source, 'command.god') or IsPlayerAceAllowed(source, 'group.god')
                hasAdmin = IsPlayerAceAllowed(source, 'command.admin') or IsPlayerAceAllowed(source, 'group.admin')
                hasMod = IsPlayerAceAllowed(source, 'command.mod') or IsPlayerAceAllowed(source, 'group.mod')
            end

            -- 调试输出权限状态
            SystemPrint("^3[权限检查] ^7管理员权限状态 - admin: " .. tostring(hasAdmin) ..
                      ", god: " .. tostring(hasGod) ..
                      ", mod: " .. tostring(hasMod))

            -- 对于QB框架，任何管理员级别都视为有权限
            local result = hasAdmin or hasGod or hasMod

            -- 特殊处理：如果是服务器控制台(source=0)，总是有权限
            if source == 0 then
                result = true
            end

            SystemPrint("^3[权限检查] ^7最终结果: " .. tostring(result))
            return result
        else
            -- 其他权限直接检查QB权限系统
            local hasPermission = QBCore.Functions.HasPermission(source, permission)
            SystemPrint("^3[权限检查] ^7其他权限检查 - 权限: " .. permission .. ", 结果: " .. tostring(hasPermission))
            return hasPermission
        end
    end
    
    return false
end

-- 检查玩家是否有彩票配置权限（彩票店职业达到adminAccess等级）
function HasLotteryConfigPermission(source)
    -- 调试输出
    SystemPrint("^3[彩票配置权限检查] ^7玩家ID: " .. source)

    -- 检查职业系统是否启用
    if Config.LotteryJob.enabled ~= true then
        SystemPrint("^3[彩票配置权限检查] ^7职业系统未启用")
        return false
    end

    local player = GetPlayer(source)
    if not player then
        SystemPrint("^3[彩票配置权限检查] ^7玩家不存在")
        return false
    end

    if Config.Framework == 'ESX' then
        local job = player.getJob()
        if not job or job.name ~= Config.LotteryJob.name then
            SystemPrint("^3[彩票配置权限检查] ^7玩家职业不匹配 - 当前职业: " .. (job and job.name or "无") .. ", 需要职业: " .. Config.LotteryJob.name)
            return false
        end

        -- 检查职业等级是否达到adminAccess要求
        local hasPermission = job.grade >= Config.LotteryJob.adminAccess
        SystemPrint("^3[彩票配置权限检查] ^7ESX职业等级检查 - 当前等级: " .. job.grade .. ", 需要等级: " .. Config.LotteryJob.adminAccess .. ", 结果: " .. tostring(hasPermission))
        return hasPermission
    elseif Config.Framework == 'QB' then
        local job = player.PlayerData.job
        if not job or job.name ~= Config.LotteryJob.name then
            SystemPrint("^3[彩票配置权限检查] ^7玩家职业不匹配 - 当前职业: " .. (job and job.name or "无") .. ", 需要职业: " .. Config.LotteryJob.name)
            return false
        end

        -- 检查职业等级是否达到adminAccess要求
        local hasPermission = job.grade.level >= Config.LotteryJob.adminAccess
        SystemPrint("^3[彩票配置权限检查] ^7QB职业等级检查 - 当前等级: " .. job.grade.level .. ", 需要等级: " .. Config.LotteryJob.adminAccess .. ", 结果: " .. tostring(hasPermission))
        return hasPermission
    end

    return false
end

-- NUI回调处理
RegisterNetEvent('lottery:closeLottery')
AddEventHandler('lottery:closeLottery', function()
    -- 客户端关闭UI时不需要服务器响应
end)

RegisterNetEvent('lottery:focus')
AddEventHandler('lottery:focus', function(data)
    local source = source
    -- 服务器端不能调用SetNuiFocus，这是客户端函数
    -- SetNuiFocus只能在客户端使用
end)

-- 打开彩票店
RegisterNetEvent('lottery:openShop', function(shopId)
    local source = source
    local player = GetPlayer(source)
    
    if not player then
        return
    end
    
    local shop = Config.Shops[shopId] or Config.Shops[1]
    
    -- 获取玩家数据
    local playerData = {
        money = GetPlayerMoney(source),
        name = GetPlayerName(source),
        identifier = GetPlayerIdentifier(source)
    }
    
    -- 发送数据到客户端
    TriggerClientEvent('lottery:openUI', source, {
        action = 'openLottery',
        shopData = shop,
        playerData = playerData,
        config = {
            doubleBall = Config.DoubleBall,
            superLotto = Config.SuperLotto,
            scratchCards = Config.ScratchCards,
            ui = Config.UI,
            debug = Config.Debug -- 添加调试模式配置
        }
    })
end)

-- 处理管理系统UI请求
RegisterNetEvent('lottery:openAdminUI')
AddEventHandler('lottery:openAdminUI', function()
    local source = source
    local player = GetPlayer(source)
    
    if not player then
        return
    end
    
    -- 检查权限，只有彩票店职业的人才能访问管理系统
    if not HasPermission(source, "lottery_shop") then
        SendNotification(source, '您没有权限访问管理系统，只有彩票店职业的人才能访问', 'error')
        return
    end
    
    -- 发送UI数据到客户端
    TriggerClientEvent('lottery:openAdminUI', source)
end)

-- 资源启动时添加缓存初始化
AddEventHandler('onResourceStart', function(resourceName)
    if (GetCurrentResourceName() ~= resourceName) then
        return
    end
    
    -- 执行数据库初始化
    SetTimeout(2000, function()
        if InitializeDatabase then
            InitializeDatabase()
        else
            print("^1[彩票系统] ^7数据库初始化函数未找到")
        end
    end)

    if Cache then
        for cacheType, _ in pairs(Cache) do
            if type(Cache[cacheType]) == "table" and cacheType ~= "queryTimes" then
                Cache[cacheType] = {}
            end
        end
        print("^2[彩票系统] ^7缓存系统已初始化")
    end
    
    -- 不再需要从文件加载配置，直接使用config.lua中的配置
    print("^2[彩票系统] ^7使用config.lua中的默认配置，配置修改只在当前会话有效")
end)

-- 资源停止时保存缓存统计
AddEventHandler('onResourceStop', function(resourceName)
    if (GetCurrentResourceName() ~= resourceName) then
        return
    end
    
    -- 记录缓存统计
    if Cache and type(Cache.PrintStats) == "function" then
        Cache:PrintStats()
        print("^3[彩票系统] ^7资源停止，缓存统计已记录")
    end
end)

-- 数据库优化管理命令
RegisterCommand('lottery_db_optimize', function(source, args, rawCommand)
    if source == 0 then -- 只允许控制台执行
        OptimizeDatabasePerformance()
        print("^2[彩票系统] ^7数据库优化已手动执行")
    end
end, true)

RegisterCommand('lottery_db_stats', function(source, args, rawCommand)
    if source == 0 then -- 只允许控制台执行
        local stats = exports[GetCurrentResourceName()]:GetDatabaseStats()
        print(string.format("^2[彩票系统] ^7数据库统计 - 查询: %d, 错误: %d, 慢查询: %d, 连接问题: %d",
            stats.queries, stats.errors, stats.slowQueries, stats.connectionIssues))
    end
end, true)

RegisterCommand('lottery_db_cleanup', function(source, args, rawCommand)
    if source == 0 then -- 只允许控制台执行
        MySQL.Async.execute('CALL CleanupOldLotteryData()', {}, function()
            print("^2[彩票系统] ^7数据库清理已手动执行")
        end)
    end
end, true)

-- 显示彩票配置页面的命令
RegisterCommand('lottery_config', function(source, args, rawCommand)
    if source == 0 then
        print("此命令只能在游戏中使用")
        return
    end

    -- 检查权限：管理员权限 或 彩票店职业达到adminAccess等级
    local hasAdminPermission = HasPermission(source, Config.Permissions.admin)
    local hasJobPermission = HasLotteryConfigPermission(source)

    SystemPrint("^3[彩票配置命令] ^7权限检查结果 - 管理员权限: " .. tostring(hasAdminPermission) .. ", 职业权限: " .. tostring(hasJobPermission))

    if not hasAdminPermission and not hasJobPermission then
        SendNotification(source, '您没有权限访问彩票配置，需要管理员权限或彩票店职业3级以上', 'error')
        return
    end

    -- 通知客户端打开管理系统并显示配置页面
    TriggerClientEvent('lottery:openAdminAndShowConfig', source)
    SendNotification(source, '正在打开彩票配置页面...', 'info')
end, false)

-- 导出函数供其他资源使用
exports('GetFramework', function()
    return Framework
end)

exports('GetPlayer', GetPlayer)
exports('GetPlayerMoney', GetPlayerMoney)
exports('AddPlayerMoney', AddPlayerMoney)
exports('RemovePlayerMoney', RemovePlayerMoney)

-- 注册网络事件处理getDrawHistory请求
RegisterNetEvent('lottery:getDrawHistory')
AddEventHandler('lottery:getDrawHistory', function(data)
    local source = source
    
    -- 获取开奖历史数据
    local lotteryType = data.lotteryType or 'double_ball'
    local drawHistory = GetDrawHistory(lotteryType, 20)
    
    -- 发送数据到客户端
    TriggerClientEvent('lottery:receiveDrawHistory', source, drawHistory)
end)

-- 接收开奖历史数据
RegisterNetEvent('lottery:receiveDrawHistory')
AddEventHandler('lottery:receiveDrawHistory', function(data)
    -- 发送到NUI
    SendNUIMessage({
        action = 'receiveDrawHistory',
        data = data
    })
end)

-- 初始化完成
SystemPrint("^2[彩票系统] ^7框架初始化完成: " .. Config.Framework)
SystemConfigLoaded = true

-- 不再需要从文件加载配置，直接使用config.lua中的配置
-- 配置修改会直接更新内存中的Config表

-- 注册通讯事件处理程序