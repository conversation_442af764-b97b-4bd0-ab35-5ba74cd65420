-- 彩票逻辑模块

-- 获取框架对象
local ESX, QBCore = nil, nil
if Config.Framework == 'ESX' then
    ESX = exports['es_extended']:getSharedObject()
elseif Config.Framework == 'QB' then
    QBCore = exports['qb-core']:GetCoreObject()
end

-- 全局变量，用于存储预设的开奖号码
local PresetWinningNumbers = {
    double_ball = nil,
    super_lotto = nil
}

-- 调试打印函数 - 只在调试模式开启时打印
local function DebugPrint(message, level)
    if Config.Debug and Config.Debug.enabled then
        level = level or "^7" -- 默认白色
        print(level .. "[彩票系统-调试] " .. message)
    end
end

-- 系统消息打印函数 - 总是打印重要的系统消息
local function SystemPrint(message, level)
    level = level or "^7" -- 默认白色
    print(level .. "[彩票系统] " .. message)
end

-- 购买双色球
RegisterNetEvent('lottery:buyDoubleBall')
AddEventHandler('lottery:buyDoubleBall', function(data)
    local source = source
    local player = GetPlayer(source)
    
    if not player then
        TriggerClientEvent('lottery:buyResult', source, {success = false, message = "玩家数据错误"})
        return
    end
    
    local playerMoney = GetPlayerMoney(source)
    local price = Config.DoubleBall.price
    
    -- 检查金钱
    if playerMoney < price then
        TriggerClientEvent('lottery:buyResult', source, {success = false, message = "余额不足"})
        return
    end
    
    -- 验证选择的号码
    if not data.redBalls or not data.blueBall then
        TriggerClientEvent('lottery:buyResult', source, {success = false, message = "请选择完整的号码"})
        return
    end
    
    if #data.redBalls ~= Config.DoubleBall.selectRed then
        TriggerClientEvent('lottery:buyResult', source, {success = false, message = "请选择" .. Config.DoubleBall.selectRed .. "个红球"})
        return
    end
    
    -- 验证号码范围
    for _, num in pairs(data.redBalls) do
        if num < 1 or num > Config.DoubleBall.redBalls then
            TriggerClientEvent('lottery:buyResult', source, {success = false, message = "红球号码超出范围"})
            return
        end
    end
    
    if data.blueBall < 1 or data.blueBall > Config.DoubleBall.blueBalls then
        TriggerClientEvent('lottery:buyResult', source, {success = false, message = "蓝球号码超出范围"})
        return
    end
    
    -- 扣除金钱
    if not RemovePlayerMoney(source, price) then
        TriggerClientEvent('lottery:buyResult', source, {success = false, message = "扣费失败"})
        return
    end
    
    -- 获取下一期开奖日期
    local drawDate = GetNextDrawDate('double_ball')
    
    -- 保存购买记录
    local playerData = {
        identifier = GetPlayerIdentifier(source),
        name = GetPlayerName(source)
    }
    
    local numbers = {
        redBalls = data.redBalls,
        blueBall = data.blueBall
    }
    
    -- 立即发送购买成功响应，提升用户体验
    local mockTicketId = os.time() * 1000 + source -- 生成一个临时票据ID
    SendNotification(source, "双色球购买成功！开奖日期：" .. drawDate, "success")
    TriggerClientEvent('lottery:buyResult', source, {success = true, ticketId = mockTicketId})

    -- 后台异步保存到数据库
    SaveLotteryTicket(playerData, 'double_ball', numbers, drawDate, function(ticketId)
        if ticketId then
            DebugPrint("双色球购买记录已保存到数据库，票据ID: " .. tostring(ticketId))

            -- 延时广播更新后的奖池数据给所有客户端，确保奖池更新已完成
            Citizen.SetTimeout(1000, function() -- 延迟1000毫秒确保奖池更新完成
                MySQL.Async.fetchAll('SELECT * FROM prize_pools ORDER BY lottery_type', {}, function(pools)
                    TriggerClientEvent('caipiaoc:receivePrizePools', -1, pools or {})
                end)
            end)
        else
            -- 保存失败，但用户已经收到成功响应，记录错误并退款
            SystemPrint("^1[彩票系统] ^7双色球购买记录保存失败，玩家: " .. tostring(playerData.name) .. ", 已退款")
            AddPlayerMoney(source, price)
            SendNotification(source, "系统异常，购买已取消并退款", "warning")
        end
    end)
end)

-- 购买大乐透
RegisterNetEvent('lottery:buySuperLotto')
AddEventHandler('lottery:buySuperLotto', function(data)
    local source = source
    local player = GetPlayer(source)
    
    if not player then
        TriggerClientEvent('lottery:buyResult', source, {success = false, message = "玩家数据错误"})
        return
    end
    
    local playerMoney = GetPlayerMoney(source)
    local price = Config.SuperLotto.price
    
    -- 检查金钱
    if playerMoney < price then
        TriggerClientEvent('lottery:buyResult', source, {success = false, message = "余额不足"})
        return
    end
    
    -- 验证选择的号码
    if not data.frontBalls or not data.backBalls then
        TriggerClientEvent('lottery:buyResult', source, {success = false, message = "请选择完整的号码"})
        return
    end
    
    if #data.frontBalls ~= Config.SuperLotto.selectFront then
        TriggerClientEvent('lottery:buyResult', source, {success = false, message = "请选择" .. Config.SuperLotto.selectFront .. "个前区球"})
        return
    end
    
    if #data.backBalls ~= Config.SuperLotto.selectBack then
        TriggerClientEvent('lottery:buyResult', source, {success = false, message = "请选择" .. Config.SuperLotto.selectBack .. "个后区球"})
        return
    end
    
    -- 验证号码范围
    for _, num in pairs(data.frontBalls) do
        if num < 1 or num > Config.SuperLotto.frontBalls then
            TriggerClientEvent('lottery:buyResult', source, {success = false, message = "前区球号码超出范围"})
            return
        end
    end
    
    for _, num in pairs(data.backBalls) do
        if num < 1 or num > Config.SuperLotto.backBalls then
            TriggerClientEvent('lottery:buyResult', source, {success = false, message = "后区球号码超出范围"})
            return
        end
    end
    
    -- 扣除金钱
    if not RemovePlayerMoney(source, price) then
        TriggerClientEvent('lottery:buyResult', source, {success = false, message = "扣费失败"})
        return
    end
    
    -- 获取下一期开奖日期
    local drawDate = GetNextDrawDate('super_lotto')
    
    -- 保存购买记录
    local playerData = {
        identifier = GetPlayerIdentifier(source),
        name = GetPlayerName(source)
    }
    
    local numbers = {
        frontBalls = data.frontBalls,
        backBalls = data.backBalls
    }
    
    -- 立即发送购买成功响应，提升用户体验
    local mockTicketId = os.time() * 1000 + source -- 生成一个临时票据ID
    SendNotification(source, "大乐透购买成功！开奖日期：" .. drawDate, "success")
    TriggerClientEvent('lottery:buyResult', source, {success = true, ticketId = mockTicketId})

    -- 后台异步保存到数据库
    SaveLotteryTicket(playerData, 'super_lotto', numbers, drawDate, function(ticketId)
        if ticketId then
            DebugPrint("大乐透购买记录已保存到数据库，票据ID: " .. tostring(ticketId))

            -- 延时广播更新后的奖池数据给所有客户端，确保奖池更新已完成
            Citizen.SetTimeout(1000, function() -- 延迟1000毫秒确保奖池更新完成
                MySQL.Async.fetchAll('SELECT * FROM prize_pools ORDER BY lottery_type', {}, function(pools)
                    TriggerClientEvent('caipiaoc:receivePrizePools', -1, pools or {})
                end)
            end)
        else
            -- 保存失败，但用户已经收到成功响应，记录错误并退款
            SystemPrint("^1[彩票系统] ^7大乐透购买记录保存失败，玩家: " .. tostring(playerData.name) .. ", 已退款")
            AddPlayerMoney(source, price)
            SendNotification(source, "系统异常，购买已取消并退款", "warning")
        end
    end)
end)

-- 获取玩家彩票记录
RegisterNetEvent('lottery:getPlayerTickets')
AddEventHandler('lottery:getPlayerTickets', function(data)
    local source = source
    local playerIdentifier = GetPlayerIdentifier(source)
    
    if not playerIdentifier then
        DebugPrint("无法获取玩家标识符: " .. tostring(source))
        TriggerClientEvent('lottery:receivePlayerTickets', source, {})
        return
    end
    
    DebugPrint("获取彩票记录请求 - 玩家ID: " .. playerIdentifier .. ", 类型: " .. tostring(data.lotteryType))
    
    -- 确保lotteryType有效值
    local lotteryType = data.lotteryType
    if not lotteryType or (lotteryType ~= 'double_ball' and lotteryType ~= 'super_lotto') then
        DebugPrint("无效的彩票类型: " .. tostring(lotteryType))
        lotteryType = 'double_ball' -- 默认使用双色球
    end
    
    -- 使用异步版本的 GetPlayerLotteryStats
    GetPlayerLotteryStats(playerIdentifier, lotteryType, function(tickets)
        -- 确保tickets始终是一个表，即使返回nil也转换为空表
        tickets = tickets or {}

        -- 添加更多调试信息
        DebugPrint("^3[彩票系统] ^7玩家彩票记录 - 类型: " .. lotteryType .. ", 找到记录数: " .. #tickets)
        if #tickets > 0 then
            DebugPrint("^3[彩票系统] ^7第一条记录示例: " .. json.encode(tickets[1]))
        end

        TriggerClientEvent('lottery:receivePlayerTickets', source, tickets)
    end)
end)

-- 获取开奖历史
RegisterNetEvent('lottery:getDrawHistory')
AddEventHandler('lottery:getDrawHistory', function(data)
    local source = source
    local lotteryType = data.lotteryType or 'double_ball'
    
    -- 添加调试信息
    DebugPrint("^3[彩票系统] ^7获取开奖历史请求 - 类型: " .. lotteryType)
    
    -- 使用异步版本的 GetDrawHistory
    GetDrawHistory(lotteryType, 20, function(drawHistory)
        drawHistory = drawHistory or {}

        -- 准备发送数据
        DebugPrint("^3[彩票系统] ^7准备发送开奖历史数据: " .. json.encode(drawHistory))

        -- 发送数据到客户端
        TriggerClientEvent('lottery:receiveDrawHistory', source, drawHistory)
    end)
end)

-- 注意：GetDrawHistory 函数已在 database.lua 中定义，这里不需要重复定义

-- 获取下一期开奖日期
function GetNextDrawDate(lotteryType)
    local config = lotteryType == 'double_ball' and Config.DoubleBall or Config.SuperLotto
    local currentDate = os.date("*t")
    local currentDay = currentDate.wday -- 1=周日, 2=周一, ..., 7=周六
    
    -- 调试信息
    DebugPrint("计算开奖日期 - 彩票类型: " .. lotteryType)
    DebugPrint("当前时间: " .. os.date("%Y-%m-%d %H:%M:%S", os.time()))
    DebugPrint("配置的开奖时间: " .. config.drawTime.hour .. ":" .. config.drawTime.minute)
    
    -- 如果是每日开奖模式（drawDays包含所有7天）
    if #config.drawDays == 7 then
        -- 检查今天是否已经过了开奖时间
        local currentHour = currentDate.hour
        local currentMinute = currentDate.min
        local drawHour = config.drawTime.hour
        local drawMinute = config.drawTime.minute
        
        -- 获取当前时间的时间戳，精确到秒
        local currentTimestamp = os.time()
        local todayDate = os.date("%Y-%m-%d")
        
        -- 构建今天的开奖时间
        local todayDrawTime = os.time({
            year = currentDate.year,
            month = currentDate.month,
            day = currentDate.day,
            hour = drawHour,
            min = drawMinute,
            sec = 0
        })
        
        DebugPrint("今天的开奖时间戳: " .. todayDrawTime)
        DebugPrint("今天的开奖时间: " .. os.date("%Y-%m-%d %H:%M:%S", todayDrawTime))
        DebugPrint("当前时间戳: " .. currentTimestamp)
        
        -- 如果当前时间晚于今天的开奖时间，返回明天的日期
        if currentTimestamp > todayDrawTime then
            local tomorrowDate = currentTimestamp + (24 * 60 * 60) -- 加一天
            local tomorrowDateStr = os.date("%Y-%m-%d", tomorrowDate)
            DebugPrint("当前时间晚于今天开奖时间，返回明天日期: " .. tomorrowDateStr)
            return tomorrowDateStr
        else
            -- 当前时间早于今天的开奖时间，返回今天的日期
            DebugPrint("当前时间早于今天开奖时间，返回今天日期: " .. todayDate)
            return todayDate
        end
    else
        -- 原有的按照特定日期开奖的逻辑
        -- 查找下一个开奖日
        local nextDrawDay = nil
        local daysToAdd = 0
        
        for _, drawDay in pairs(config.drawDays) do
            local adjustedDay = drawDay == 7 and 1 or drawDay + 1 -- 转换为Lua的星期格式
            if adjustedDay >= currentDay then
                nextDrawDay = adjustedDay
                daysToAdd = adjustedDay - currentDay
                break
            end
        end
        
        -- 如果没找到本周的开奖日，使用下周的第一个开奖日
        if not nextDrawDay then
            local firstDrawDay = config.drawDays[1]
            nextDrawDay = firstDrawDay == 7 and 1 or firstDrawDay + 1
            daysToAdd = 7 - currentDay + nextDrawDay
        end
        
        local nextDate = os.time() + (daysToAdd * 24 * 60 * 60)
        local nextDateStr = os.date("%Y-%m-%d", nextDate)
        DebugPrint("下一个开奖日期: " .. nextDateStr)
        return nextDateStr
    end
end

-- 生成开奖号码
function GenerateWinningNumbers(lotteryType)
    local numbers = {}
    
    if lotteryType == 'double_ball' then
        -- 生成6个不重复的红球
        local redBalls = {}
        while #redBalls < Config.DoubleBall.selectRed do
            local num = math.random(1, Config.DoubleBall.redBalls)
            local exists = false
            for _, existingNum in pairs(redBalls) do
                if existingNum == num then
                    exists = true
                    break
                end
            end
            if not exists then
                table.insert(redBalls, num)
            end
        end
        table.sort(redBalls)
        
        -- 生成1个蓝球
        local blueBall = math.random(1, Config.DoubleBall.blueBalls)
        
        numbers = {
            redBalls = redBalls,
            blueBall = blueBall,
            formatted = table.concat(redBalls, ', ') .. ' | ' .. blueBall
        }
        
    elseif lotteryType == 'super_lotto' then
        -- 生成5个不重复的前区球
        local frontBalls = {}
        while #frontBalls < Config.SuperLotto.selectFront do
            local num = math.random(1, Config.SuperLotto.frontBalls)
            local exists = false
            for _, existingNum in pairs(frontBalls) do
                if existingNum == num then
                    exists = true
                    break
                end
            end
            if not exists then
                table.insert(frontBalls, num)
            end
        end
        table.sort(frontBalls)
        
        -- 生成2个不重复的后区球
        local backBalls = {}
        while #backBalls < Config.SuperLotto.selectBack do
            local num = math.random(1, Config.SuperLotto.backBalls)
            local exists = false
            for _, existingNum in pairs(backBalls) do
                if existingNum == num then
                    exists = true
                    break
                end
            end
            if not exists then
                table.insert(backBalls, num)
            end
        end
        table.sort(backBalls)
        
        numbers = {
            frontBalls = frontBalls,
            backBalls = backBalls,
            formatted = table.concat(frontBalls, ', ') .. ' | ' .. table.concat(backBalls, ', ')
        }
    end
    
    return numbers
end

-- 检查中奖
function CheckWinning(lotteryType, playerNumbers, winningNumbers)
    local config = lotteryType == 'double_ball' and Config.DoubleBall or Config.SuperLotto
    
    -- 安全检查，确保配置和中奖号码存在
    if not config or not winningNumbers then
        SystemPrint("^1[彩票系统] ^7CheckWinning错误: 配置或中奖号码为空")
        return 0, 0
    end
    
    -- 确保prizes存在
    if not config.prizes then
        SystemPrint("^1[彩票系统] ^7CheckWinning错误: 奖项配置为空")
        return 0, 0
    end
    
    if lotteryType == 'double_ball' then
        local redMatches = 0
        local blueMatch = 0
        
        -- 确保号码格式正确
        if not playerNumbers.redBalls or not winningNumbers.redBalls or 
           not playerNumbers.blueBall or not winningNumbers.blueBall then
            SystemPrint("^1[彩票系统] ^7CheckWinning错误: 双色球号码格式不正确")
            return 0, 0
        end
        
        -- 计算红球匹配数
        for _, playerRed in pairs(playerNumbers.redBalls) do
            for _, winningRed in pairs(winningNumbers.redBalls) do
                if playerRed == winningRed then
                    redMatches = redMatches + 1
                    break
                end
            end
        end
        
        -- 计算蓝球匹配
        if playerNumbers.blueBall == winningNumbers.blueBall then
            blueMatch = 1
        end
        
        -- 查找奖项
        for level, prize in pairs(config.prizes) do
            if prize.match and prize.match[1] == redMatches and prize.match[2] == blueMatch then
                local prizeAmount = prize.amount or 0
                
                -- 如果是一等奖或二等奖，需要加上奖池的百分比
                if prize.poolPercent and prize.poolPercent > 0 then
                    local poolAmount = GetPrizePoolAmount(lotteryType) or 0
                    local additionalAmount = math.floor(poolAmount * prize.poolPercent)
                    prizeAmount = prizeAmount + additionalAmount
                    
                    SystemPrint(string.format("^2[彩票系统] ^7%s %s奖金计算: 固定奖金%d + 奖池(%d)的%.0f%% = %d",
                        lotteryType == 'double_ball' and '双色球' or '大乐透',
                        prize.name or ("" .. level .. "等奖"),
                        prize.amount or 0,
                        poolAmount,
                        prize.poolPercent * 100,
                        prizeAmount
                    ))
                end
                
                return level, prizeAmount
            end
        end
    elseif lotteryType == 'super_lotto' then
        local frontMatches = 0
        local backMatches = 0
        
        -- 确保号码格式正确
        if not playerNumbers.frontBalls or not winningNumbers.frontBalls or 
           not playerNumbers.backBalls or not winningNumbers.backBalls then
            SystemPrint("^1[彩票系统] ^7CheckWinning错误: 大乐透号码格式不正确")
            return 0, 0
        end
        
        -- 计算前区球匹配数
        for _, playerFront in pairs(playerNumbers.frontBalls) do
            for _, winningFront in pairs(winningNumbers.frontBalls) do
                if playerFront == winningFront then
                    frontMatches = frontMatches + 1
                    break
                end
            end
        end
        
        -- 计算后区球匹配数
        for _, playerBack in pairs(playerNumbers.backBalls) do
            for _, winningBack in pairs(winningNumbers.backBalls) do
                if playerBack == winningBack then
                    backMatches = backMatches + 1
                    break
                end
            end
        end
        
        -- 查找奖项
        for level, prize in pairs(config.prizes) do
            if prize.match and prize.match[1] == frontMatches and prize.match[2] == backMatches then
                local prizeAmount = prize.amount or 0
                
                -- 如果是一等奖或二等奖，需要加上奖池的百分比
                if prize.poolPercent and prize.poolPercent > 0 then
                    local poolAmount = GetPrizePoolAmount(lotteryType) or 0
                    local additionalAmount = math.floor(poolAmount * prize.poolPercent)
                    prizeAmount = prizeAmount + additionalAmount
                    
                    SystemPrint(string.format("^2[彩票系统] ^7%s %s奖金计算: 固定奖金%d + 奖池(%d)的%.0f%% = %d",
                        lotteryType == 'double_ball' and '双色球' or '大乐透',
                        prize.name or ("" .. level .. "等奖"),
                        prize.amount or 0,
                        poolAmount,
                        prize.poolPercent * 100,
                        prizeAmount
                    ))
                end
                
                return level, prizeAmount
            end
        end
    end
    
    return 0, 0 -- 未中奖
end

-- 彩票开奖命令 (管理员)
RegisterCommand('lottery_draw', function(source, args, rawCommand)
    if source == 0 or HasPermission(source, Config.Permissions.drawLottery) then
        -- 检查参数
        if not args[1] then
            if source > 0 then
                SendNotification(source, "用法: /lottery_draw [double_ball|super_lotto] [numbers] [period_number]", "info")
            else
                SystemPrint("用法: lottery_draw [double_ball|super_lotto] [numbers] [period_number]")
            end
            return
        end
        
        local lotteryType = args[1]
        if lotteryType ~= 'double_ball' and lotteryType ~= 'super_lotto' then
            local message = "无效的彩票类型，请使用 double_ball 或 super_lotto"
            if source > 0 then
                SendNotification(source, message, "error")
            else
                SystemPrint(message)
            end
            return
        end
        
        -- 使用当前时间而不是固定日期
        local currentTime = os.time()
        local drawDate = os.date("%Y-%m-%d", currentTime)
        
        -- 解析指定的号码，如果有的话
        local winningNumbers = nil
        if args[2] then
            local numbers = {}
            for num in string.gmatch(args[2], "%d+") do
                table.insert(numbers, tonumber(num))
            end
            
            -- 检查号码数量
            local requiredCount = lotteryType == 'double_ball' and 7 or 7 -- 双色球需要6红1蓝，大乐透需要5前2后
            if #numbers ~= requiredCount then
                local message = "号码数量不正确，双色球需要7个号码(6红1蓝)，大乐透需要7个号码(5前2后)"
                if source > 0 then
                    SendNotification(source, message, "error")
                else
                    SystemPrint(message)
                end
                return
            end
            
            -- 生成开奖号码
            if lotteryType == 'double_ball' then
                winningNumbers = {
                    redBalls = {numbers[1], numbers[2], numbers[3], numbers[4], numbers[5], numbers[6]},
                    blueBall = numbers[7],
                    formatted = table.concat({numbers[1], numbers[2], numbers[3], numbers[4], numbers[5], numbers[6]}, ", ") .. " | " .. numbers[7]
                }
            elseif lotteryType == 'super_lotto' then
                winningNumbers = {
                    frontBalls = {numbers[1], numbers[2], numbers[3], numbers[4], numbers[5]},
                    backBalls = {numbers[6], numbers[7]},
                    formatted = table.concat({numbers[1], numbers[2], numbers[3], numbers[4], numbers[5]}, ", ") .. " | " .. table.concat({numbers[6], numbers[7]}, ", ")
                }
            end
        else
            -- 如果没有指定号码，则随机生成
            winningNumbers = GenerateWinningNumbers(lotteryType)
        end
        
        -- 使用指定的期号，如果有的话
        local periodNumber = args[3] or GeneratePeriodNumber(lotteryType)
        
        SystemPrint("^2[彩票系统] ^7手动开奖 - 彩票类型: " .. lotteryType .. ", 指定期号: " .. periodNumber)
        
        -- 计算销售额等数据
        local salesData = CalculateSalesData(lotteryType, drawDate)
        
        -- 保存开奖结果
        local drawId = SaveDrawResult(lotteryType, periodNumber, drawDate, winningNumbers, salesData)
        if not drawId then
            SystemPrint("^1[彩票系统] ^7保存开奖结果失败")
            return
        end
        
        -- 计算中奖彩票，传递第四个参数表示这是手动开奖
        CalculateWinningTickets(lotteryType, winningNumbers, periodNumber, true, function(result)
            local totalWinners = result.totalWinners or 0
            local winners = result.winners or {}
            salesData.totalWinners = totalWinners

            -- 更新开奖结果
            MySQL.Async.execute('UPDATE draw_history SET total_winners = ?, prize_details = ? WHERE id = ?', {
                totalWinners,
                json.encode(prizeDistribution),
                drawId
            })
        end)
        
        -- 广播开奖结果（移除重复的通知代码，BroadcastDrawResult函数内部已经处理了通知）
        BroadcastDrawResult(lotteryType, winningNumbers, periodNumber)

        -- 格式化开奖号码用于显示
        local formattedNumbers = winningNumbers.formatted or "未知号码"
        if not winningNumbers.formatted then
            if lotteryType == 'double_ball' and winningNumbers.redBalls and winningNumbers.blueBall then
                formattedNumbers = table.concat(winningNumbers.redBalls, ', ') .. ' | ' .. winningNumbers.blueBall
                winningNumbers.formatted = formattedNumbers
            elseif lotteryType == 'super_lotto' and winningNumbers.frontBalls and winningNumbers.backBalls then
                formattedNumbers = table.concat(winningNumbers.frontBalls, ', ') .. ' | ' .. table.concat(winningNumbers.backBalls, ', ')
                winningNumbers.formatted = formattedNumbers
            end
        end

        -- 提示成功消息
        local successMessage = string.format("手动开奖成功 - 彩票类型: %s, 期号: %s, 中奖号码: %s",
            lotteryType,
            periodNumber,
            formattedNumbers
        )
        
        if source > 0 then
            SendNotification(source, successMessage, "success")
        else
            SystemPrint("^2[彩票系统] ^7" .. successMessage)
        end
        
        -- 重要：不要触发自动开奖
        -- 这里不调用ConductDraw函数，避免触发自动开奖
    else
        SendNotification(source, "权限不足", "error")
    end
end, false)

-- 彩票开奖命令 (管理员) - 旧命令的别名，调用新命令
RegisterCommand('lottery_draw', function(source, args, rawCommand)
    -- 将旧命令格式转换为新命令格式
    local newArgs = {}
    
    -- 添加彩票类型
    if args[1] then
        table.insert(newArgs, args[1])
        
        -- 添加号码参数
        if args[2] then
            table.insert(newArgs, "numbers=" .. args[2])
        end
        
        -- 添加期号参数
        if args[3] then
            table.insert(newArgs, "period=" .. args[3])
        end
    end
    
    -- 创建新的命令字符串
    local newCommand = "lottery_manual_draw"
    if #newArgs > 0 then
        newCommand = newCommand .. " " .. table.concat(newArgs, " ")
    end
    
    -- 调用新命令
    ExecuteCommand(newCommand)
end, false)

-- 新版手动开奖命令 (管理员)
RegisterCommand('lottery_manual_draw', function(source, args, rawCommand)
    print("^2[彩票系统] ^7玩家 " .. source .. " 尝试使用手动开奖命令")
    
    -- 检查权限
    local hasPermission = false
    if source == 0 then -- 控制台总是有权限
        hasPermission = true
        print("^2[彩票系统] ^7控制台执行命令，自动授权")
    elseif Config.Framework == 'QB' then
        -- QB框架权限检查 - 使用多种方式确保兼容性
        local hasGod = false
        local hasAdmin = false
        local hasMod = false

        -- 方法1: 使用QBCore.Functions.HasPermission (如果存在)
        if QBCore.Functions.HasPermission then
            hasGod = QBCore.Functions.HasPermission(source, 'god')
            hasAdmin = QBCore.Functions.HasPermission(source, 'admin')
            hasMod = QBCore.Functions.HasPermission(source, 'mod')
        end

        -- 方法2: 检查玩家数据中的权限组
        if not (hasGod or hasAdmin or hasMod) then
            local Player = QBCore.Functions.GetPlayer(source)
            if Player and Player.PlayerData then
                -- 检查权限组
                if Player.PlayerData.permission then
                    local permission = Player.PlayerData.permission
                    hasGod = permission == 'god'
                    hasAdmin = permission == 'admin'
                    hasMod = permission == 'mod'
                end

                -- 检查职业是否为管理员相关
                if Player.PlayerData.job and Player.PlayerData.job.name then
                    local jobName = Player.PlayerData.job.name
                    if jobName == 'admin' or jobName == 'police' or jobName == 'government' then
                        hasAdmin = true
                    end
                end
            end
        end

        -- 方法3: 使用IsPlayerAceAllowed (FiveM原生权限系统)
        if not (hasGod or hasAdmin or hasMod) then
            hasGod = IsPlayerAceAllowed(source, 'command.god') or IsPlayerAceAllowed(source, 'group.god')
            hasAdmin = IsPlayerAceAllowed(source, 'command.admin') or IsPlayerAceAllowed(source, 'group.admin')
            hasMod = IsPlayerAceAllowed(source, 'command.mod') or IsPlayerAceAllowed(source, 'group.mod')
        end

        print("^3[彩票系统] ^7QB权限状态 - god: " .. tostring(hasGod) ..
              ", admin: " .. tostring(hasAdmin) ..
              ", mod: " .. tostring(hasMod))

        -- 任何管理员级别都可以执行命令
        hasPermission = hasGod or hasAdmin or hasMod

        -- 如果常规权限检查失败，尝试使用通用HasPermission函数
        if not hasPermission then
            print("^3[彩票系统] ^7QB常规权限检查失败，尝试通用权限检查")
            hasPermission = HasPermission(source, Config.Permissions.drawLottery)
        end
    else
        -- ESX框架使用通用权限检查
        hasPermission = HasPermission(source, Config.Permissions.drawLottery)
    end
    
    if hasPermission then
        print("^2[彩票系统] ^7玩家 " .. source .. " 通过权限检查")
    else
        print("^1[彩票系统] ^7玩家 " .. source .. " 权限不足，无法使用手动开奖命令")
        SendNotification(source, "权限不足，无法使用手动开奖命令", "error")
        return
    end
    
    -- 通过权限检查，继续执行
    -- 检查参数
    if not args[1] then
        local helpMessage = [[
用法:
/lottery_manual_draw [彩票类型] [选项]

彩票类型:
- double_ball: 双色球
- super_lotto: 大乐透

选项:
- numbers=[号码]: 指定开奖号码(如 numbers=1,2,3,4,5,6,7)
- period=[期号]: 指定期号(如 period=2023001)
- date=[日期]: 指定开奖日期，格式为YYYY-MM-DD(如 date=2023-10-01)

示例:
/lottery_manual_draw double_ball
/lottery_manual_draw super_lotto numbers=1,2,3,4,5,6,7
/lottery_manual_draw double_ball period=2023001 numbers=1,2,3,4,5,6,7
/lottery_manual_draw super_lotto date=2023-10-01
        ]]
        
        if source > 0 then
            SendNotification(source, "手动开奖命令帮助", "info")
            -- 分多条消息发送，避免过长
            for _, line in ipairs(SplitStringByLines(helpMessage)) do
                TriggerClientEvent('chat:addMessage', source, {color = {255, 255, 128}, multiline = false, args = {"彩票系统", line}})
            end
        else
            SystemPrint(helpMessage)
        end
        return
    end
        
        -- 解析参数
        local lotteryType = args[1]
        if lotteryType ~= 'double_ball' and lotteryType ~= 'super_lotto' then
            local message = "无效的彩票类型，请使用 double_ball(双色球) 或 super_lotto(大乐透)"
            if source > 0 then
                SendNotification(source, message, "error")
            else
                SystemPrint(message)
            end
            return
        end
        
        -- 初始化默认值
        local userNumbers = nil
        local periodNumber = nil
        local drawDate = os.date("%Y-%m-%d") -- 默认为今天
        
        -- 解析选项
        for i = 2, #args do
            local arg = args[i]
            
            -- 解析号码
            if string.find(arg, "numbers=") then
                local numbersStr = string.sub(arg, 9) -- 去掉"numbers="
                userNumbers = {}
                for num in string.gmatch(numbersStr, "%d+") do
                    table.insert(userNumbers, tonumber(num))
                end
            end
            
            -- 解析期号
            if string.find(arg, "period=") then
                periodNumber = string.sub(arg, 8) -- 去掉"period="
            end
            
            -- 解析日期
            if string.find(arg, "date=") then
                local dateStr = string.sub(arg, 6) -- 去掉"date="
                if string.match(dateStr, "%d%d%d%d%-%d%d%-%d%d") then
                    drawDate = dateStr
                else
                    local message = "日期格式无效，请使用YYYY-MM-DD格式"
                    if source > 0 then
                        SendNotification(source, message, "error")
                    else
                        SystemPrint(message)
                    end
                    return
                end
            end
        end
        
        -- 验证和生成开奖号码
        local winningNumbers = nil
        
        -- 如果有用户指定的号码，则验证
        if userNumbers then
            -- 检查号码数量
            local requiredCount = lotteryType == 'double_ball' and 7 or 7 -- 双色球需要6红1蓝，大乐透需要5前2后
            if #userNumbers ~= requiredCount then
                local message = string.format(
                    "号码数量不正确，%s需要%d个号码(%s)",
                    lotteryType == 'double_ball' and '双色球' or '大乐透',
                    requiredCount,
                    lotteryType == 'double_ball' and '6红1蓝' or '5前2后'
                )
                if source > 0 then
                    SendNotification(source, message, "error")
                else
                    SystemPrint(message)
                end
                return
            end
            
            -- 检查号码范围
            local isValid = true
            local errorMsg = ""
            
            if lotteryType == 'double_ball' then
                -- 双色球: 6个红球(1-33)，1个蓝球(1-16)
                for i = 1, 6 do
                    if userNumbers[i] < 1 or userNumbers[i] > 33 then
                        isValid = false
                        errorMsg = string.format("红球 #%d (%d) 超出范围，红球范围为1-33", i, userNumbers[i])
                        break
                    end
                end
                
                if isValid and (userNumbers[7] < 1 or userNumbers[7] > 16) then
                    isValid = false
                    errorMsg = string.format("蓝球 (%d) 超出范围，蓝球范围为1-16", userNumbers[7])
                end
                
                -- 检查红球是否有重复
                local redBalls = {}
                for i = 1, 6 do
                    if redBalls[userNumbers[i]] then
                        isValid = false
                        errorMsg = string.format("红球 #%d (%d) 重复", i, userNumbers[i])
                        break
                    end
                    redBalls[userNumbers[i]] = true
                end
            elseif lotteryType == 'super_lotto' then
                -- 大乐透: 5个前区号码(1-35)，2个后区号码(1-12)
                for i = 1, 5 do
                    if userNumbers[i] < 1 or userNumbers[i] > 35 then
                        isValid = false
                        errorMsg = string.format("前区号码 #%d (%d) 超出范围，前区范围为1-35", i, userNumbers[i])
                        break
                    end
                end
                
                if isValid then
                    for i = 6, 7 do
                        if userNumbers[i] < 1 or userNumbers[i] > 12 then
                            isValid = false
                            errorMsg = string.format("后区号码 #%d (%d) 超出范围，后区范围为1-12", i-5, userNumbers[i])
                            break
                        end
                    end
                end
                
                -- 检查前区号码是否有重复
                local frontBalls = {}
                for i = 1, 5 do
                    if frontBalls[userNumbers[i]] then
                        isValid = false
                        errorMsg = string.format("前区号码 #%d (%d) 重复", i, userNumbers[i])
                        break
                    end
                    frontBalls[userNumbers[i]] = true
                end
                
                -- 检查后区号码是否有重复
                local backBalls = {}
                for i = 6, 7 do
                    if backBalls[userNumbers[i]] then
                        isValid = false
                        errorMsg = string.format("后区号码 #%d (%d) 重复", i-5, userNumbers[i])
                        break
                    end
                    backBalls[userNumbers[i]] = true
                end
            end
            
            if not isValid then
                if source > 0 then
                    SendNotification(source, errorMsg, "error")
                else
                    SystemPrint(errorMsg)
                end
                return
            end
            
            -- 生成开奖号码对象
            if lotteryType == 'double_ball' then
                winningNumbers = {
                    redBalls = {userNumbers[1], userNumbers[2], userNumbers[3], userNumbers[4], userNumbers[5], userNumbers[6]},
                    blueBall = userNumbers[7],
                    formatted = table.concat({userNumbers[1], userNumbers[2], userNumbers[3], userNumbers[4], userNumbers[5], userNumbers[6]}, ", ") .. " | " .. userNumbers[7]
                }
            elseif lotteryType == 'super_lotto' then
                winningNumbers = {
                    frontBalls = {userNumbers[1], userNumbers[2], userNumbers[3], userNumbers[4], userNumbers[5]},
                    backBalls = {userNumbers[6], userNumbers[7]},
                    formatted = table.concat({userNumbers[1], userNumbers[2], userNumbers[3], userNumbers[4], userNumbers[5]}, ", ") .. " | " .. table.concat({userNumbers[6], userNumbers[7]}, ", ")
                }
            end
        else
            -- 随机生成开奖号码
            winningNumbers = GenerateWinningNumbers(lotteryType)
        end
        
        -- 如果未指定期号，则生成
        if not periodNumber then
            periodNumber = GeneratePeriodNumber(lotteryType)
        end
        
        -- 开始开奖流程
        SystemPrint("^2[彩票系统] ^7手动开奖 - 彩票类型: " .. lotteryType .. ", 指定期号: " .. periodNumber .. ", 开奖日期: " .. drawDate)
        
        -- 计算销售额等数据
        local salesData = CalculateSalesData(lotteryType, drawDate)
        
        -- 保存开奖结果到数据库
        SaveDrawResult(lotteryType, periodNumber, drawDate, winningNumbers, salesData, function(drawId)
            if not drawId then
                local errorMsg = "保存开奖结果失败"
                if source > 0 then
                    SendNotification(source, errorMsg, "error")
                else
                    SystemPrint("^1[彩票系统] ^7" .. errorMsg)
                end
                return
            end

            -- 计算中奖彩票，传递第四个参数表示这是手动开奖
            CalculateWinningTickets(lotteryType, winningNumbers, periodNumber, true, function(result)
                local totalWinners = result.totalWinners or 0
                local winners = result.winners or {}
                local prizeDistribution = {}

                -- 检查是否找到了符合条件的彩票
                if not winners or next(winners) == nil then
                    local noTicketsMessage = "没有找到符合条件的彩票，请确认以下几点：\n1. 是否有玩家购买了" .. (lotteryType == 'double_ball' and '双色球' or '大乐透') .. "彩票\n2. 这些彩票是否已经参与过开奖\n3. 数据库中是否存在彩票记录"

                    if source > 0 then
                        SendNotification(source, "未找到符合条件的彩票", "warning")

                        for _, line in ipairs(SplitStringByLines(noTicketsMessage)) do
                            TriggerClientEvent('chat:addMessage', source, {color = {255, 200, 0}, multiline = false, args = {"彩票系统", line}})
                        end
                    else
                        SystemPrint("^3[彩票系统] ^7" .. noTicketsMessage)
                    end

                    -- 即使没有找到符合条件的彩票，也继续保存开奖结果
                    DebugPrint("^3[彩票系统] ^7尽管没有符合条件的彩票，开奖结果仍将保存")
                end

                -- 统计各奖级的中奖人数
                for prizeLevel, winnerList in pairs(winners or {}) do
                    prizeDistribution[prizeLevel] = #winnerList
                end

                -- 更新销售数据中的中奖信息
                salesData.totalWinners = totalWinners
                salesData.prizeDistribution = prizeDistribution

                -- 更新开奖结果
                MySQL.Async.execute('UPDATE draw_history SET total_winners = ?, prize_details = ? WHERE id = ?', {
                    totalWinners,
                    json.encode(prizeDistribution),
                    drawId
                })

                -- 广播开奖结果给所有在线玩家
                BroadcastDrawResult(lotteryType, winningNumbers, periodNumber)

                -- 格式化开奖号码用于显示
                local formattedNumbers = winningNumbers.formatted or "未知号码"
                if not winningNumbers.formatted then
                    if lotteryType == 'double_ball' and winningNumbers.redBalls and winningNumbers.blueBall then
                        formattedNumbers = table.concat(winningNumbers.redBalls, ', ') .. ' | ' .. winningNumbers.blueBall
                        winningNumbers.formatted = formattedNumbers
                    elseif lotteryType == 'super_lotto' and winningNumbers.frontBalls and winningNumbers.backBalls then
                        formattedNumbers = table.concat(winningNumbers.frontBalls, ', ') .. ' | ' .. table.concat(winningNumbers.backBalls, ', ')
                        winningNumbers.formatted = formattedNumbers
                    end
                end

                -- 构建成功消息
                local successMessage = string.format("手动开奖成功 - 彩票类型: %s, 期号: %s, 开奖日期: %s, 中奖号码: %s, 中奖人数: %d",
                    lotteryType == 'double_ball' and '双色球' or '大乐透',
                    periodNumber,
                    drawDate,
                    formattedNumbers,
                    totalWinners
                )

                -- 发送成功通知
                if source > 0 then
                    SendNotification(source, successMessage, "success")

                    -- 发送中奖详情
                    local prizeDetails = "中奖详情:\n"
                    for level, count in pairs(prizeDistribution) do
                        prizeDetails = prizeDetails .. string.format("- %d等奖: %d人\n", level, count)
                    end

                    for _, line in ipairs(SplitStringByLines(prizeDetails)) do
                        TriggerClientEvent('chat:addMessage', source, {color = {100, 255, 100}, multiline = false, args = {"彩票系统", line}})
                    end
                else
                    SystemPrint("^2[彩票系统] ^7" .. successMessage)

                    -- 打印中奖详情
                    SystemPrint("^2[彩票系统] ^7中奖详情:")
                    for level, count in pairs(prizeDistribution) do
                        SystemPrint(string.format("^2[彩票系统] ^7- %d等奖: %d人", level, count))
                    end
                end

                -- 记录管理员操作日志
                if source > 0 then
                    local player = GetPlayerFromId(source)
                    if player then
                        LogAdminAction(player, 'manual_draw', string.format("手动开奖 - 类型:%s 期号:%s",
                            lotteryType == 'double_ball' and '双色球' or '大乐透',
                            periodNumber
                        ), 0)
                    end
                end
            end)
        end)
    end, false)

-- 辅助函数：按行分割字符串
function SplitStringByLines(str)
    local lines = {}
    for line in string.gmatch(str, "[^\r\n]+") do
        table.insert(lines, line)
    end
    return lines
end

-- 执行开奖
function ConductDraw(lotteryType)
    -- 获取当前日期
    local currentDate = os.date("%Y-%m-%d")
    local periodNumber = GetCurrentPeriodNumber(lotteryType)
    
    SystemPrint("^2[彩票系统] ^7执行开奖 - 彩票类型: " .. lotteryType .. ", 生成期号: " .. periodNumber)
    
    -- 获取配置的是否检查今日开奖状态
    local config = lotteryType == 'double_ball' and Config.DoubleBall or Config.SuperLotto
    local checkDrawToday = config.checkDrawToday
    
    -- 如果配置为检查今日开奖状态，则检查是否已经开过奖
    if checkDrawToday then
        -- 使用缓存检查今天是否已经开过奖
        local cacheKey = "draw_status_" .. lotteryType .. "_" .. currentDate
        local cachedStatus = Cache and Cache:Get("drawStatus", cacheKey)

        if cachedStatus == true then
            SystemPrint("^3[彩票系统] ^7今天已经开过奖，跳过开奖过程")
            return false
        end
    end
    
    -- 检查是否有预设的开奖号码
    local winningNumbers = nil
    if PresetWinningNumbers[lotteryType] then
        winningNumbers = PresetWinningNumbers[lotteryType]
        SystemPrint("^2[彩票系统] ^7使用预设的开奖号码: " .. (winningNumbers.formatted or "未知格式"))
        -- 使用后清除预设号码
        PresetWinningNumbers[lotteryType] = nil
    else
        -- 根据彩票类型生成中奖号码
        if lotteryType == 'double_ball' then
            winningNumbers = GenerateDoubleBallNumbers()
        elseif lotteryType == 'super_lotto' then
            winningNumbers = GenerateSuperLottoNumbers()
        else
            SystemPrint("^1[彩票系统] ^7不支持的彩票类型: " .. lotteryType)
            return false
        end
    end
    
    if not winningNumbers then
        SystemPrint("^1[彩票系统] ^7生成中奖号码失败")
        return false
    end
    
    -- 保存开奖结果
    local salesData = {
        totalSales = 0,
        totalWinners = 0,
        prizeDetails = {},
        jackpotAmount = 0
    }
    
    SaveDrawResult(lotteryType, periodNumber, currentDate, winningNumbers, salesData, function(drawId)
        if not drawId then
            SystemPrint("^1[彩票系统] ^7保存开奖结果失败")
            return false
        end

        -- 计算中奖彩票
        CalculateWinningTickets(lotteryType, winningNumbers, periodNumber, false, function(result)
            -- 更新开奖结果的获奖信息
            local totalWinners = result.totalWinners or 0
            local winners = result.winners or {}

            -- 更新开奖结果
            MySQL.Async.execute('UPDATE draw_history SET total_winners = ? WHERE id = ?', {
                totalWinners,
                drawId
            })

            -- 更新开奖状态缓存
            if Cache then
                local cacheKey = "draw_status_" .. lotteryType .. "_" .. currentDate
                Cache:Set("drawStatus", cacheKey, true, 3600) -- 缓存1小时
                DebugPrint("^3[彩票系统] ^7已更新开奖状态缓存: " .. cacheKey)
            end

            SystemPrint("^2[彩票系统] ^7开奖完成 - 彩票类型: " .. lotteryType .. ", 中奖人数: " .. totalWinners)

            -- 广播开奖结果给所有在线玩家
            BroadcastDrawResult(lotteryType, winningNumbers, periodNumber)

            -- 额外确保通知发送成功
            local lotteryName = lotteryType == 'double_ball' and '双色球' or '大乐透'
            local formattedNumbers = winningNumbers.formatted or "未知号码"
            local notificationMessage = lotteryName .. "自动开奖完成: " .. formattedNumbers .. " (第" .. periodNumber .. "期)"

            -- 发送全服通知
            SendServerNotification(notificationMessage, "success")

            -- 记录通知发送
            DebugPrint("^2[彩票系统] ^7已发送自动开奖通知: " .. notificationMessage)
        end)
    end)

    return true
end

-- 生成期号
function GeneratePeriodNumber(lotteryType)
    -- 查询数据库中该类型最大期号
    local lastPeriod = MySQL.Sync.fetchScalar([[
        SELECT IFNULL(MAX(CAST(SUBSTRING_INDEX(period_number, '-', -1) AS UNSIGNED)), 0) 
        FROM draw_history 
        WHERE lottery_type = ?
    ]], {lotteryType})
    
    -- 获取下一期期号
    local nextPeriod = tonumber(lastPeriod) + 1
    local periodNumber = string.format("%04d", nextPeriod)
    
    DebugPrint("^3[彩票系统] ^7生成期号 - 彩票类型: " .. lotteryType .. ", 期号: " .. periodNumber)
    
    return periodNumber
end

-- 计算销售数据
function CalculateSalesData(lotteryType, drawDate)
    local result = MySQL.Async.fetchScalar(
        'SELECT COUNT(*) as total_sales FROM lottery_tickets WHERE lottery_type = ? AND DATE(purchase_time) = ?',
        {lotteryType, drawDate}
    )
    
    local price = lotteryType == 'double_ball' and Config.DoubleBall.price or Config.SuperLotto.price
    local totalSales = (result or 0) * price
    
    return {
        totalSales = totalSales,
        totalTickets = result or 0,
        prizeDetails = {},
        jackpotAmount = GetPrizePoolAmount(lotteryType)
    }
end

-- 检查是否到了开奖时间
function CheckDrawTime(lotteryType)
    -- 获取当前时间
    local currentDateTime = os.date("%Y-%m-%d %H:%M:%S")
    local currentDate = os.date("%Y-%m-%d")
    local currentHour = tonumber(os.date("%H"))
    local currentMinute = tonumber(os.date("%M"))
    local currentSecond = tonumber(os.date("%S"))
    
    -- 获取配置的开奖时间
    local config = lotteryType == 'double_ball' and Config.DoubleBall or Config.SuperLotto
    local drawHour = config.drawTime.hour
    local drawMinute = config.drawTime.minute
    
    -- 构建今天的开奖时间
    local todayDrawTime = string.format("%s %02d:%02d:00", currentDate, drawHour, drawMinute)
    
    -- 调试日志
    -- 只在配置启用时显示详细的开奖时间检查日志
    if Config.Debug and Config.Debug.showDrawTimeCheck then
        DebugPrint("^3[彩票系统] ^7检查开奖时间 - 彩票类型: " .. lotteryType)
        DebugPrint("^3[彩票系统] ^7当前时间: " .. currentDateTime)
        DebugPrint("^3[彩票系统] ^7开奖时间: " .. todayDrawTime)
    end
    
    -- 计算当前时间和开奖时间的时间差（秒）
    local currentTimestamp = os.time({
        year = tonumber(os.date("%Y")),
        month = tonumber(os.date("%m")),
        day = tonumber(os.date("%d")),
        hour = currentHour,
        min = currentMinute,
        sec = currentSecond
    })
    
    local drawTimestamp = os.time({
        year = tonumber(os.date("%Y")),
        month = tonumber(os.date("%m")),
        day = tonumber(os.date("%d")),
        hour = drawHour,
        min = drawMinute,
        sec = 0
    })
    
    local timeDiff = currentTimestamp - drawTimestamp
    
    -- 检查是否在开奖时间后的60秒内
    local isAfterDrawTime = (timeDiff > 0 and timeDiff <= 60)
    
    if Config.Debug and Config.Debug.showDrawTimeCheck then
        DebugPrint("^3[彩票系统] ^7时间差（秒）: " .. timeDiff)
        DebugPrint("^3[彩票系统] ^7是否在开奖时间后60秒内: " .. tostring(isAfterDrawTime))
    end
    
    -- 检查今天是否是开奖日
    local isDrawDay = false
    local dayOfWeek = tonumber(os.date("%w")) -- 0是周日，1-6是周一到周六
    
    -- 调整为1-7，其中7是周日
    if dayOfWeek == 0 then dayOfWeek = 7 end
    
    for _, day in ipairs(config.drawDays) do
        if day == dayOfWeek then
            isDrawDay = true
            break
        end
    end
    
    if Config.Debug and Config.Debug.showDrawTimeCheck then
        DebugPrint("^3[彩票系统] ^7今天是周" .. dayOfWeek .. ", 是否开奖日: " .. tostring(isDrawDay))
    end
    
    -- 检查今天是否已经开过奖
    local hasDrawnToday = false
    
    -- 获取是否需要检查今日开奖状态的配置
    local checkDrawToday = config.checkDrawToday
    if Config.Debug and Config.Debug.showDrawTimeCheck then
        DebugPrint("^3[彩票系统] ^7是否检查今日开奖状态: " .. tostring(checkDrawToday))
    end
    
    -- 如果配置为检查今日开奖状态
    if checkDrawToday then
        -- 获取当前期号
        local periodNumber = GetCurrentPeriodNumber(lotteryType)

        -- 使用缓存检查今天是否已经开过奖
        local cacheKey = "draw_status_" .. lotteryType .. "_" .. currentDate
        local cachedStatus = Cache and Cache:Get("drawStatus", cacheKey)

        if cachedStatus ~= nil then
            hasDrawnToday = cachedStatus
            if Config.Debug and Config.Debug.showDrawTimeCheck then
                DebugPrint("^3[彩票系统] ^7从缓存获取开奖状态: " .. tostring(hasDrawnToday))
            end
        else
            -- 缓存未命中，异步查询数据库（但不阻塞当前检查）
            MySQL.Async.fetchAll('SELECT id FROM draw_history WHERE lottery_type = ? AND DATE(draw_date) = ? LIMIT 1', {
                lotteryType,
                currentDate
            }, function(result)
                local drawn = result and #result > 0
                if Cache then
                    -- 缓存结果，有效期1小时
                    Cache:Set("drawStatus", cacheKey, drawn, 3600)
                end
                if Config.Debug and Config.Debug.showDrawTimeCheck then
                    DebugPrint("^3[彩票系统] ^7数据库查询开奖状态: " .. tostring(drawn))
                end
            end)

            -- 首次查询时假设未开奖，避免阻塞
            hasDrawnToday = false
            if Config.Debug and Config.Debug.showDrawTimeCheck then
                DebugPrint("^3[彩票系统] ^7首次查询，假设未开奖")
            end
        end
    else
        if Config.Debug and Config.Debug.showDrawTimeCheck then
            DebugPrint("^3[彩票系统] ^7已关闭今日开奖状态检查，忽略是否已开奖")
        end
    end
    
    -- 根据配置决定是否考虑hasDrawnToday因素
    local shouldDraw
    if checkDrawToday then
        -- 如果是开奖日、在开奖时间后的60秒内且今天尚未开过奖，则可以开奖
        shouldDraw = isDrawDay and isAfterDrawTime and not hasDrawnToday
    else
        -- 如果不检查今日开奖状态，则只要是开奖日且在开奖时间后的60秒内就可以开奖
        shouldDraw = isDrawDay and isAfterDrawTime
    end
    
    if Config.Debug and Config.Debug.showDrawTimeCheck then
        DebugPrint("^3[彩票系统] ^7是否应该开奖: " .. tostring(shouldDraw))
    end

    -- 如果不检查今日开奖状态，则总是生成新的期号
    local periodNumber = GetCurrentPeriodNumber(lotteryType)

    if Config.Debug and Config.Debug.showDrawTimeCheck then
        DebugPrint("^3[彩票系统] ^7获取当前期号 - 彩票类型: " .. lotteryType .. ", 期号: " .. periodNumber)
    end

    return shouldDraw, periodNumber
end

-- 自动开奖定时任务 (优化检查频率)
CreateThread(function()
    -- 服务器启动时稍等一会儿，确保数据库已经初始化
    Wait(60000)

    local lastCheckHour = -1

    while true do
        local currentHour = tonumber(os.date("%H"))
        local currentMinute = tonumber(os.date("%M"))

        -- 只在开奖时间附近检查（开奖前30分钟到开奖后10分钟）
        local shouldCheck = false

        -- 获取开奖时间配置
        local doubleBallHour = Config.DoubleBall.drawTime.hour
        local superLottoHour = Config.SuperLotto.drawTime.hour

        -- 检查是否在开奖时间窗口内
        if (currentHour >= doubleBallHour - 1 and currentHour <= doubleBallHour + 1) or
           (currentHour >= superLottoHour - 1 and currentHour <= superLottoHour + 1) then
            shouldCheck = true
        end

        -- 如果在检查窗口内，或者是新的小时（避免遗漏）
        if shouldCheck or currentHour ~= lastCheckHour then
            -- 检查双色球
            if CheckDrawTime('double_ball') then
                DebugPrint('^3[彩票系统] ^7执行双色球自动开奖')
                ConductDraw('double_ball')
            end

            -- 检查大乐透
            if CheckDrawTime('super_lotto') then
                DebugPrint('^3[彩票系统] ^7执行大乐透自动开奖')
                ConductDraw('super_lotto')
            end

            lastCheckHour = currentHour

            -- 在开奖时间窗口内每分钟检查，其他时间每10分钟检查
            Wait(shouldCheck and 60000 or 600000)
        else
            -- 非开奖时间，每10分钟检查一次
            Wait(600000)
        end
    end
end)

-- 获取当前期号
function GetCurrentPeriodNumber(lotteryType)
    -- 查询数据库中该类型最大期号
    local lastPeriod = MySQL.Sync.fetchScalar([[
        SELECT IFNULL(MAX(CAST(SUBSTRING_INDEX(period_number, '-', -1) AS UNSIGNED)), 0) 
        FROM draw_history 
        WHERE lottery_type = ?
    ]], {lotteryType})
    
    -- 获取下一期期号
    local nextPeriod = tonumber(lastPeriod) + 1
    local periodNumber = string.format("%04d", nextPeriod)
    
    DebugPrint("^3[彩票系统] ^7获取当前期号 - 彩票类型: " .. lotteryType .. ", 期号: " .. periodNumber)
    
    return periodNumber
end

-- 生成双色球中奖号码
function GenerateDoubleBallNumbers()
    -- 生成6个红球（1-33）
    local redBalls = {}
    while #redBalls < 6 do
        local num = math.random(1, 33)
        local exists = false
        for _, v in ipairs(redBalls) do
            if v == num then
                exists = true
                break
            end
        end
        if not exists then
            table.insert(redBalls, num)
        end
    end
    
    -- 对红球进行排序
    table.sort(redBalls)
    
    -- 生成1个蓝球（1-16）
    local blueBall = math.random(1, 16)
    
    DebugPrint("^3[彩票系统] ^7生成双色球中奖号码 - 红球: " .. table.concat(redBalls, ",") .. ", 蓝球: " .. blueBall)
    
    return {
        redBalls = redBalls,
        blueBall = blueBall,
        formatted = table.concat(redBalls, ', ') .. ' | ' .. blueBall
    }
end

-- 生成大乐透中奖号码
function GenerateSuperLottoNumbers()
    -- 生成5个前区号码（1-35）
    local frontBalls = {}
    while #frontBalls < 5 do
        local num = math.random(1, 35)
        local exists = false
        for _, v in ipairs(frontBalls) do
            if v == num then
                exists = true
                break
            end
        end
        if not exists then
            table.insert(frontBalls, num)
        end
    end
    
    -- 对前区号码进行排序
    table.sort(frontBalls)
    
    -- 生成2个后区号码（1-12）
    local backBalls = {}
    while #backBalls < 2 do
        local num = math.random(1, 12)
        local exists = false
        for _, v in ipairs(backBalls) do
            if v == num then
                exists = true
                break
            end
        end
        if not exists then
            table.insert(backBalls, num)
        end
    end
    
    -- 对后区号码进行排序
    table.sort(backBalls)
    
    DebugPrint("^3[彩票系统] ^7生成大乐透中奖号码 - 前区: " .. table.concat(frontBalls, ",") .. ", 后区: " .. table.concat(backBalls, ","))
    
    return {
        frontBalls = frontBalls,
        backBalls = backBalls,
        formatted = table.concat(frontBalls, ', ') .. ' | ' .. table.concat(backBalls, ', ')
    }
end

-- 广播开奖结果
function BroadcastDrawResult(lotteryType, winningNumbers, periodNumber)
    -- 构建开奖结果消息
    local lotteryName = lotteryType == 'double_ball' and '双色球' or '大乐透'
    
    -- 确保winningNumbers和formatted字段存在
    if not winningNumbers then
        DebugPrint("^1[彩票系统] ^7广播开奖结果失败: 中奖号码为空")
        return
    end
    
    -- 如果formatted字段不存在，则创建它
    local formattedNumbers = ""
    if winningNumbers.formatted then
        formattedNumbers = winningNumbers.formatted
    else
        -- 根据彩票类型构建格式化的中奖号码
        if lotteryType == 'double_ball' and winningNumbers.redBalls and winningNumbers.blueBall then
            formattedNumbers = table.concat(winningNumbers.redBalls, ', ') .. ' | ' .. winningNumbers.blueBall
            -- 更新winningNumbers对象，添加formatted字段
            winningNumbers.formatted = formattedNumbers
        elseif lotteryType == 'super_lotto' and winningNumbers.frontBalls and winningNumbers.backBalls then
            formattedNumbers = table.concat(winningNumbers.frontBalls, ', ') .. ' | ' .. table.concat(winningNumbers.backBalls, ', ')
            -- 更新winningNumbers对象，添加formatted字段
            winningNumbers.formatted = formattedNumbers
        else
            formattedNumbers = "未知号码"
            DebugPrint("^1[彩票系统] ^7警告: 无法格式化中奖号码")
        end
    end
    
    -- 构建消息文本
    local message = lotteryName .. "开奖结果: " .. formattedNumbers
    
    -- 添加期号信息
    if periodNumber then
        -- 格式化期号显示
        local displayPeriodNumber = periodNumber
        if not string.find(periodNumber, lotteryType) then
            displayPeriodNumber = lotteryType .. "-" .. periodNumber
        end
        message = message .. " (第" .. displayPeriodNumber .. "期)"
    end
    
    -- 只在聊天窗口全服广播，不再触发客户端弹窗
    SendServerNotification(message, "info")
    
    DebugPrint("^2[彩票系统] ^7已广播开奖结果 - 彩票类型: " .. lotteryType .. ", 期号: " .. periodNumber)
end

-- 注意：GetPlayerLotteryStats 函数已在 database.lua 中定义，这里不需要重复定义

-- 测试通知功能的命令
RegisterCommand('test_lottery_notification', function(source, args, rawCommand)
    if source == 0 or IsPlayerAceAllowed(source, 'lottery.admin') then
        local testMessage = "这是一条测试通知消息"

        -- 测试个人通知
        if source > 0 then
            SendNotification(source, "个人通知测试: " .. testMessage, "info")
        end

        -- 测试全服通知
        SendServerNotification("全服通知测试: " .. testMessage, "success")

        -- 测试开奖结果广播
        local testWinningNumbers = {
            redBalls = {1, 5, 12, 18, 25, 32},
            blueBall = 7,
            formatted = "01, 05, 12, 18, 25, 32 | 07"
        }
        BroadcastDrawResult('double_ball', testWinningNumbers, 'TEST-001')

        SystemPrint("^2[彩票系统] ^7通知功能测试完成")
    else
        SendNotification(source, "权限不足", "error")
    end
end, false)

-- 强制中奖命令
RegisterCommand('lottery_force_win', function(source, args, rawCommand)
    if source == 0 or HasPermission(source, Config.Permissions.admin) then
        -- 检查参数
        if not args[1] or not args[2] or not args[3] then
            local message = "用法: /lottery_force_win [player_id] [double_ball|super_lotto] [prize_level]"
            if source > 0 then
                SendNotification(source, message, "info")
            else
                SystemPrint(message)
            end
            return
        end
        
        local targetId = tonumber(args[1])
        local lotteryType = args[2]
        local prizeLevel = tonumber(args[3])
        
        -- 验证彩票类型
        if lotteryType ~= 'double_ball' and lotteryType ~= 'super_lotto' then
            local message = "无效的彩票类型，请使用 double_ball 或 super_lotto"
            if source > 0 then
                SendNotification(source, message, "error")
            else
                SystemPrint(message)
            end
            return
        end
        
        -- 验证奖项级别
        local config = lotteryType == 'double_ball' and Config.DoubleBall or Config.SuperLotto
        if not config.prizes[prizeLevel] then
            local message = "无效的奖项级别，双色球和大乐透支持1-6级奖"
            if source > 0 then
                SendNotification(source, message, "error")
            else
                SystemPrint(message)
            end
            return
        end
        
        -- 获取目标玩家
        local targetPlayer = GetPlayer(targetId)
        if not targetPlayer then
            local message = "找不到ID为 " .. targetId .. " 的玩家"
            if source > 0 then
                SendNotification(source, message, "error")
            else
                SystemPrint(message)
            end
            return
        end
        
        -- 生成中奖号码
        local winningNumbers = nil
        if lotteryType == 'double_ball' then
            winningNumbers = GenerateDoubleBallNumbers()
        else
            winningNumbers = GenerateSuperLottoNumbers()
        end
        
        -- 获取玩家标识符
        local playerIdentifier = GetPlayerIdentifier(targetId)
        local playerName = GetPlayerName(targetId)
        
        -- 创建一张彩票
        local ticketData = {
            player_id = playerIdentifier,
            lottery_type = lotteryType,
            purchase_time = os.date("%Y-%m-%d %H:%M:%S"),
            numbers = json.encode(winningNumbers),
            is_winning = 1,
            prize_level = prizeLevel,
            prize_amount = config.prizes[prizeLevel].amount,
            is_claimed = 0
        }
        
        -- 保存到数据库
        MySQL.Async.insert('INSERT INTO lottery_tickets (player_id, lottery_type, purchase_time, numbers, is_winning, prize_level, prize_amount, is_claimed) VALUES (?, ?, ?, ?, ?, ?, ?, ?)', {
            ticketData.player_id,
            ticketData.lottery_type,
            ticketData.purchase_time,
            ticketData.numbers,
            ticketData.is_winning,
            ticketData.prize_level,
            ticketData.prize_amount,
            ticketData.is_claimed
        }, function(ticketId)
            if ticketId and ticketId > 0 then
                -- 成功创建中奖彩票
                local successMessage = string.format("已为玩家 %s 创建一张%s %d等奖彩票，奖金：%s%d",
                    playerName,
                    lotteryType == 'double_ball' and '双色球' or '大乐透',
                    prizeLevel,
                    Config.Currency.symbol,
                    config.prizes[prizeLevel].amount
                )
                
                if source > 0 then
                    SendNotification(source, successMessage, "success")
                else
                    SystemPrint("^2[彩票系统] ^7" .. successMessage)
                end
                
                -- 通知目标玩家
                SendNotification(targetId, "恭喜您！您购买的彩票中了大奖！请前往彩票店查看详情。", "success")
                
                -- 更新玩家的未领取奖品
                TriggerClientEvent('lottery:updateUnclaimedPrizes', targetId)
            else
                -- 创建失败
                local errorMessage = "创建中奖彩票失败"
                if source > 0 then
                    SendNotification(source, errorMessage, "error")
                else
                    SystemPrint("^1[彩票系统] ^7" .. errorMessage)
                end
            end
        end)
    else
        SendNotification(source, "权限不足", "error")
    end
end, false)

-- 发送开奖结果通知
function SendDrawResultNotification(drawData)
    -- 检查是否有效的开奖数据
    if not drawData or not drawData.lotteryType or not drawData.winningNumbers then
        SystemPrint("^1[彩票系统] ^7发送开奖结果通知失败: 无效的开奖数据")
        return
    end
    
    -- 构建开奖结果消息
    local message = ""
    local lotteryName = ""
    
    if drawData.lotteryType == "double_ball" then
        lotteryName = "双色球"
    elseif drawData.lotteryType == "super_lotto" then
        lotteryName = "大乐透"
    else
        lotteryName = drawData.lotteryType
    end
    
    -- 确保formatted字段存在
    local formattedNumbers = ""
    if drawData.winningNumbers.formatted then
        formattedNumbers = drawData.winningNumbers.formatted
    else
        -- 根据彩票类型构建格式化的中奖号码
        local winningNumbers = drawData.winningNumbers
        if drawData.lotteryType == "double_ball" and winningNumbers.redBalls and winningNumbers.blueBall then
            formattedNumbers = table.concat(winningNumbers.redBalls, ', ') .. ' | ' .. winningNumbers.blueBall
            -- 更新winningNumbers对象，添加formatted字段
            winningNumbers.formatted = formattedNumbers
        elseif drawData.lotteryType == "super_lotto" and winningNumbers.frontBalls and winningNumbers.backBalls then
            formattedNumbers = table.concat(winningNumbers.frontBalls, ', ') .. ' | ' .. table.concat(winningNumbers.backBalls, ', ')
            -- 更新winningNumbers对象，添加formatted字段
            winningNumbers.formatted = formattedNumbers
        else
            formattedNumbers = "未知号码"
            SystemPrint("^1[彩票系统] ^7警告: 无法格式化中奖号码")
        end
    end
    
    message = lotteryName .. "开奖结果: " .. formattedNumbers
    
    -- 添加期号信息
    if drawData.periodNumber then
        message = message .. " (第" .. drawData.periodNumber .. "期)"
    end
    
    -- 添加中奖人数信息
    if drawData.totalWinners ~= nil then
        message = message .. " 共有" .. drawData.totalWinners .. "人中奖"
    end
    
    -- 只在聊天窗口全服广播，不再弹窗
    SendServerNotification(message, "info")
    
    -- 记录到日志
    SystemPrint("^2[彩票系统] ^7已发送开奖结果通知: " .. message)
end

-- 添加查询彩票命令 (管理员)
RegisterCommand('lottery_check_tickets', function(source, args, rawCommand)
    if source == 0 or HasPermission(source, Config.Permissions.drawLottery) then
        local lotteryType = args[1]
        if not lotteryType or (lotteryType ~= 'double_ball' and lotteryType ~= 'super_lotto') then
            local message = "用法: /lottery_check_tickets [double_ball|super_lotto]"
            if source > 0 then
                SendNotification(source, message, "info")
            else
                SystemPrint(message)
            end
            return
        end
        
        -- 查询所有该类型的彩票
        local allTickets = MySQL.Sync.fetchAll('SELECT COUNT(*) as count FROM lottery_tickets WHERE lottery_type = ?', {lotteryType})
        local waitingTickets = MySQL.Sync.fetchAll('SELECT COUNT(*) as count FROM lottery_tickets WHERE lottery_type = ? AND (draw_period IS NULL OR draw_period = "") AND is_winning = 0', {lotteryType})
        local claimedTickets = MySQL.Sync.fetchAll('SELECT COUNT(*) as count FROM lottery_tickets WHERE lottery_type = ? AND is_claimed = 1', {lotteryType})
        local winningTickets = MySQL.Sync.fetchAll('SELECT COUNT(*) as count FROM lottery_tickets WHERE lottery_type = ? AND is_winning = 1', {lotteryType})
        
        local totalCount = allTickets and allTickets[1] and allTickets[1].count or 0
        local waitingCount = waitingTickets and waitingTickets[1] and waitingTickets[1].count or 0
        local claimedCount = claimedTickets and claimedTickets[1] and claimedTickets[1].count or 0
        local winningCount = winningTickets and winningTickets[1] and winningTickets[1].count or 0
        
        local message = string.format("彩票统计 (%s):\n总数: %d 张\n待开奖: %d 张\n已中奖: %d 张\n已兑奖: %d 张", 
            lotteryType == 'double_ball' and '双色球' or '大乐透',
            totalCount, waitingCount, winningCount, claimedCount)
        
        if source > 0 then
            SendNotification(source, "彩票统计", "info")
            for _, line in ipairs(SplitStringByLines(message)) do
                TriggerClientEvent('chat:addMessage', source, {color = {100, 200, 255}, multiline = false, args = {"彩票系统", line}})
            end
        else
            SystemPrint("^3[彩票系统] ^7" .. message)
        end
        
        -- 如果待开奖的彩票数量为0，检查是否有错误
        if waitingCount == 0 and totalCount > 0 then
            local errorCheck = "警告：没有找到符合开奖条件的彩票。可能的原因：\n1. 所有彩票都已经参与过开奖\n2. 数据库字段值异常"
            
            if source > 0 then
                for _, line in ipairs(SplitStringByLines(errorCheck)) do
                    TriggerClientEvent('chat:addMessage', source, {color = {255, 100, 100}, multiline = false, args = {"彩票系统", line}})
                end
            else
                SystemPrint("^1[彩票系统] ^7" .. errorCheck)
            end
            
            -- 检查可能错误的记录
            local problematicTickets = MySQL.Sync.fetchAll('SELECT id, draw_period, is_winning FROM lottery_tickets WHERE lottery_type = ? LIMIT 5', {lotteryType})
            if problematicTickets and #problematicTickets > 0 then
                local sampleMessage = "示例记录:\n"
                for i, ticket in ipairs(problematicTickets) do
                    sampleMessage = sampleMessage .. string.format("ID: %d, 期号: %s, 是否中奖: %s\n", 
                        ticket.id,
                        ticket.draw_period or "NULL",
                        ticket.is_winning == 1 and "是" or "否"
                    )
                end
                
                if source > 0 then
                    for _, line in ipairs(SplitStringByLines(sampleMessage)) do
                        TriggerClientEvent('chat:addMessage', source, {color = {255, 100, 100}, multiline = false, args = {"彩票系统", line}})
                    end
                else
                    SystemPrint("^1[彩票系统] ^7" .. sampleMessage)
                end
            end
        end
    else
        SendNotification(source, "权限不足", "error")
    end
end, false)

-- 添加修复命令 (管理员)
RegisterCommand('lottery_fix_tickets', function(source, args, rawCommand)
    if source == 0 or HasPermission(source, Config.Permissions.admin) then
        local lotteryType = args[1]
        if not lotteryType or (lotteryType ~= 'double_ball' and lotteryType ~= 'super_lotto' and lotteryType ~= 'all') then
            local message = "用法: /lottery_fix_tickets [double_ball|super_lotto|all]"
            if source > 0 then
                SendNotification(source, message, "info")
            else
                SystemPrint(message)
            end
            return
        end
        
        -- 构建SQL查询
        local query = ''
        local params = {}
        if lotteryType == 'all' then
            query = 'UPDATE lottery_tickets SET draw_period = NULL, is_winning = 0, prize_level = 0, prize_amount = 0 WHERE is_claimed = 0'
        else
            query = 'UPDATE lottery_tickets SET draw_period = NULL, is_winning = 0, prize_level = 0, prize_amount = 0 WHERE lottery_type = ? AND is_claimed = 0'
            params = {lotteryType}
        end
        
        -- 执行更新
        MySQL.Async.execute(query, params, function(rowsChanged)
            local message = string.format("已重置 %d 张未兑奖彩票的开奖状态", rowsChanged)
            
            if source > 0 then
                SendNotification(source, message, "success")
            else
                SystemPrint("^2[彩票系统] ^7" .. message)
            end
        end)
    else
        SendNotification(source, "权限不足", "error")
    end
end, false)

-- 获取刮刮乐统计
RegisterNetEvent('lottery:getScratchStats')
AddEventHandler('lottery:getScratchStats', function()
    local source = source
    local playerIdentifier = GetPlayerIdentifier(source)
    
    if not playerIdentifier then
        DebugPrint("^1[彩票系统] ^7获取刮刮乐统计错误: 无法获取玩家标识符")
        TriggerClientEvent('lottery:receiveScratchStats', source, {})
        return
    end
    
    DebugPrint("^3[彩票系统] ^7获取刮刮乐统计 - 玩家ID: " .. playerIdentifier)

    -- 使用异步版本的 GetPlayerLotteryStats
    GetPlayerLotteryStats(playerIdentifier, 'scratch', function(stats)
        -- 确保返回的是数组，即使是空的
        stats = stats or {}

        if #stats == 0 then
            DebugPrint("^3[彩票系统] ^7玩家 " .. playerIdentifier .. " 没有刮刮乐记录，返回空数组")
        else
            DebugPrint("^2[彩票系统] ^7玩家 " .. playerIdentifier .. " 刮刮乐记录数量: " .. #stats)
            -- 确保每个记录的total_prize是数字类型
            for i, stat in ipairs(stats) do
                -- 确保total_prize不为nil
                if stat.total_prize == nil then
                    stat.total_prize = 0
                end

                -- 确保total_prize是数字类型
                if type(stat.total_prize) == "string" then
                    stat.total_prize = tonumber(stat.total_prize) or 0
                end

                DebugPrint("^2[彩票系统] ^7刮刮乐类型: " .. stat.card_type ..
                    ", 数量: " .. stat.count ..
                    ", 奖金类型: " .. type(stat.total_prize) ..
                    ", 总奖金: " .. (stat.total_prize or 0))
            end
        end

        TriggerClientEvent('lottery:receiveScratchStats', source, stats)
    end)
end)

-- 检查玩家是否为彩票店老板
function IsLotteryShopOwner(source)
    local playerIdentifier = GetPlayerIdentifier(source)
    if not playerIdentifier then return false end
    
    -- 如果配置中有指定彩票店老板列表，则检查玩家是否在列表中
    if Config.LotteryShopOwners and type(Config.LotteryShopOwners) == "table" then
        for _, owner in ipairs(Config.LotteryShopOwners) do
            if owner == playerIdentifier then
                return true
            end
        end
    end
    
    -- 检查玩家是否拥有彩票店老板职位（框架特定的检查）
    if Config.Framework == 'ESX' then
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer and (xPlayer.getJob().name == 'lottery_shop' or xPlayer.getJob().name == 'caipiao') then
            -- 直接检查是否为老板职位
            local jobGradeName = xPlayer.getJob().grade_name
            if jobGradeName and jobGradeName == 'boss' then
                print("^2[彩票系统] ^7玩家 " .. source .. " 是彩票店老板，职位: " .. jobGradeName)
                return true
            else
                print("^1[彩票系统] ^7玩家 " .. source .. " 不是彩票店老板，当前职位: " .. (jobGradeName or "未知"))
                return false
            end
        end
    elseif Config.Framework == 'QB' then
        local Player = QBCore.Functions.GetPlayer(source)
        if Player and (Player.PlayerData.job.name == 'lottery_shop' or Player.PlayerData.job.name == 'caipiao') then
            -- 直接检查是否为老板
            local isBoss = Player.PlayerData.job.isboss
            if isBoss then
                print("^2[彩票系统] ^7玩家 " .. source .. " 是彩票店老板")
                return true
            else
                print("^1[彩票系统] ^7玩家 " .. source .. " 不是彩票店老板")
                return false
            end
        end
    end
    
    return false
end

-- 预设开奖号码命令
RegisterCommand('kaijiang', function(source, args, rawCommand)
    print("^2[彩票系统] ^7玩家 " .. source .. " 尝试使用预设开奖号码命令")
    
    -- 检查权限 - 只允许彩票店老板使用
    local hasPermission = false
    
    -- 控制台不再有权限
    if source == 0 then
        print("^1[彩票系统] ^7控制台无法使用该命令，此命令仅供彩票店老板使用")
        return
    end
    
    -- 检查是否为彩票店老板
    local isShopOwner = IsLotteryShopOwner(source)
    if isShopOwner then
        print("^2[彩票系统] ^7玩家 " .. source .. " 是彩票店老板，已授权")
        hasPermission = true
    end
    
    if not hasPermission then
        print("^1[彩票系统] ^7玩家 " .. source .. " 权限不足，此命令仅限彩票店老板使用")
        SendNotification(source, "此命令仅限彩票店老板使用", "error")
        return
    end
    
    -- 检查参数
    if not args[1] then
        local helpMessage = [[
用法:
/kaijiang [彩票类型] [号码]

彩票类型:
- ssq 或 double_ball: 双色球 (需要7个号码: 6红1蓝)
- dlt 或 super_lotto: 大乐透 (需要7个号码: 5前2后)

示例:
/kaijiang ssq 1,2,3,4,5,6,7
/kaijiang dlt 1,2,3,4,5,6,7
/kaijiang double_ball 1,2,3,4,5,6,7
/kaijiang clear ssq (清除预设号码)
        ]]
        
        SendNotification(source, "预设开奖号码命令帮助", "info")
        -- 分多条消息发送，避免过长
        for _, line in ipairs(SplitStringByLines(helpMessage)) do
            TriggerClientEvent('chat:addMessage', source, {color = {255, 255, 128}, multiline = false, args = {"彩票系统", line}})
        end
        return
    end
    
    -- 处理清除预设号码的情况
    if args[1] == "clear" then
        local lotteryType = args[2]
        -- 支持简写形式
        if lotteryType == 'ssq' then
            lotteryType = 'double_ball'
        elseif lotteryType == 'dlt' then
            lotteryType = 'super_lotto'
        end

        if lotteryType == "double_ball" or lotteryType == "super_lotto" then
            PresetWinningNumbers[lotteryType] = nil
            local message = string.format("已清除%s的预设开奖号码",
                lotteryType == 'double_ball' and '双色球' or '大乐透')

            if source > 0 then
                SendNotification(source, message, "success")
            else
                SystemPrint("^2[彩票系统] ^7" .. message)
            end
            return
        else
            local message = "无效的彩票类型，请使用 ssq/double_ball(双色球) 或 dlt/super_lotto(大乐透)"
            if source > 0 then
                SendNotification(source, message, "error")
            else
                SystemPrint(message)
            end
            return
        end
    end
    
    -- 解析参数
    local lotteryType = args[1]
    -- 支持简写形式
    if lotteryType == 'ssq' then
        lotteryType = 'double_ball'
    elseif lotteryType == 'dlt' then
        lotteryType = 'super_lotto'
    end

    if lotteryType ~= 'double_ball' and lotteryType ~= 'super_lotto' then
        local message = "无效的彩票类型，请使用 ssq/double_ball(双色球) 或 dlt/super_lotto(大乐透)"
        if source > 0 then
            SendNotification(source, message, "error")
        else
            SystemPrint(message)
        end
        return
    end
    
    -- 解析号码
    local userNumbers = {}
    if args[2] then
        -- 将逗号分隔的号码字符串转换为数字数组
        for num in string.gmatch(args[2], "%d+") do
            table.insert(userNumbers, tonumber(num))
        end
    else
        local message = "请提供开奖号码，格式为逗号分隔的数字，例如: 1,2,3,4,5,6,7"
        if source > 0 then
            SendNotification(source, message, "error")
        else
            SystemPrint(message)
        end
        return
    end
    
    -- 验证号码数量
    local requiredCount = lotteryType == 'double_ball' and 7 or 7 -- 双色球需要6红1蓝，大乐透需要5前2后
    if #userNumbers ~= requiredCount then
        local message = string.format(
            "号码数量不正确，%s需要%d个号码(%s)",
            lotteryType == 'double_ball' and '双色球' or '大乐透',
            requiredCount,
            lotteryType == 'double_ball' and '6红1蓝' or '5前2后'
        )
        if source > 0 then
            SendNotification(source, message, "error")
        else
            SystemPrint(message)
        end
        return
    end
    
    -- 检查号码范围和重复
    local isValid = true
    local errorMsg = ""
    
    if lotteryType == 'double_ball' then
        -- 双色球: 6个红球(1-33)，1个蓝球(1-16)
        for i = 1, 6 do
            if userNumbers[i] < 1 or userNumbers[i] > 33 then
                isValid = false
                errorMsg = string.format("红球 #%d (%d) 超出范围，红球范围为1-33", i, userNumbers[i])
                break
            end
        end
        
        if isValid and (userNumbers[7] < 1 or userNumbers[7] > 16) then
            isValid = false
            errorMsg = string.format("蓝球 (%d) 超出范围，蓝球范围为1-16", userNumbers[7])
        end
        
        -- 检查红球是否有重复
        local redBalls = {}
        for i = 1, 6 do
            if redBalls[userNumbers[i]] then
                isValid = false
                errorMsg = string.format("红球 #%d (%d) 重复", i, userNumbers[i])
                break
            end
            redBalls[userNumbers[i]] = true
        end
    elseif lotteryType == 'super_lotto' then
        -- 大乐透: 5个前区号码(1-35)，2个后区号码(1-12)
        for i = 1, 5 do
            if userNumbers[i] < 1 or userNumbers[i] > 35 then
                isValid = false
                errorMsg = string.format("前区号码 #%d (%d) 超出范围，前区范围为1-35", i, userNumbers[i])
                break
            end
        end
        
        if isValid then
            for i = 6, 7 do
                if userNumbers[i] < 1 or userNumbers[i] > 12 then
                    isValid = false
                    errorMsg = string.format("后区号码 #%d (%d) 超出范围，后区范围为1-12", i-5, userNumbers[i])
                    break
                end
            end
        end
        
        -- 检查前区号码是否有重复
        local frontBalls = {}
        for i = 1, 5 do
            if frontBalls[userNumbers[i]] then
                isValid = false
                errorMsg = string.format("前区号码 #%d (%d) 重复", i, userNumbers[i])
                break
            end
            frontBalls[userNumbers[i]] = true
        end
        
        -- 检查后区号码是否有重复
        local backBalls = {}
        for i = 6, 7 do
            if backBalls[userNumbers[i]] then
                isValid = false
                errorMsg = string.format("后区号码 #%d (%d) 重复", i-5, userNumbers[i])
                break
            end
            backBalls[userNumbers[i]] = true
        end
    end
    
    if not isValid then
        if source > 0 then
            SendNotification(source, errorMsg, "error")
        else
            SystemPrint(errorMsg)
        end
        return
    end
    
    -- 号码验证通过，创建开奖号码对象
    local winningNumbers = nil
    if lotteryType == 'double_ball' then
        winningNumbers = {
            redBalls = {userNumbers[1], userNumbers[2], userNumbers[3], userNumbers[4], userNumbers[5], userNumbers[6]},
            blueBall = userNumbers[7],
            formatted = table.concat({userNumbers[1], userNumbers[2], userNumbers[3], userNumbers[4], userNumbers[5], userNumbers[6]}, ", ") .. " | " .. userNumbers[7]
        }
    elseif lotteryType == 'super_lotto' then
        winningNumbers = {
            frontBalls = {userNumbers[1], userNumbers[2], userNumbers[3], userNumbers[4], userNumbers[5]},
            backBalls = {userNumbers[6], userNumbers[7]},
            formatted = table.concat({userNumbers[1], userNumbers[2], userNumbers[3], userNumbers[4], userNumbers[5]}, ", ") .. " | " .. table.concat({userNumbers[6], userNumbers[7]}, ", ")
        }
    end
    
    -- 保存预设号码
    PresetWinningNumbers[lotteryType] = winningNumbers

    -- 获取下一次开奖时间
    local config = lotteryType == 'double_ball' and Config.DoubleBall or Config.SuperLotto
    local drawTime = string.format("%02d:%02d", config.drawTime.hour, config.drawTime.minute)

    -- 成功消息
    local successMessage = string.format("已成功预设%s下次开奖号码: %s，将在下次开奖时间(%s)使用",
        lotteryType == 'double_ball' and '双色球' or '大乐透',
        winningNumbers.formatted,
        drawTime
    )
    
    -- 发送成功通知
    if source > 0 then
        SendNotification(source, successMessage, "success")
    else
        SystemPrint("^2[彩票系统] ^7" .. successMessage)
    end
    
    -- 记录管理员操作日志
    if source > 0 then
        local player = GetPlayerFromId(source)
        if player then
            LogAdminAction(player, 'preset_draw_numbers', string.format("预设开奖号码 - 类型:%s 号码:%s",
                lotteryType == 'double_ball' and '双色球' or '大乐透',
                winningNumbers.formatted
            ), 0)
        end
    end
end, false)